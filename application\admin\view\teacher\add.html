<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Job_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-job_number" data-rule="required" class="form-control" name="row[job_number]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-gender" data-rule="required" class="form-control selectpicker" name="row[gender]">
                {foreach name="genderList" item="vo"}
                    <option value="{$key}" {in name="key" value="male"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone" data-rule="required" class="form-control" name="row[phone]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Major')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-major" class="form-control" name="row[major]" type="text">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

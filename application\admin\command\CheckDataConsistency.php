<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 数据一致性检查命令
 * 用于检查外键关联的数据一致性问题
 */
class CheckDataConsistency extends Command
{
    protected function configure()
    {
        $this->setName('check:data-consistency')
            ->setDescription('检查数据一致性，特别是外键关联问题');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始检查数据一致性...');
        
        $issues = [];
        
        // 1. 检查学生表中的外键问题
        $issues = array_merge($issues, $this->checkStudentForeignKeys($output));
        
        // 2. 检查随迁子女报名表中的外键问题
        $issues = array_merge($issues, $this->checkMigrantForeignKeys($output));
        
        // 3. 检查高考报名表中的外键问题
        $issues = array_merge($issues, $this->checkGaokaogdForeignKeys($output));
        
        // 4. 检查证书表中的外键问题
        $issues = array_merge($issues, $this->checkCertificateForeignKeys($output));
        
        // 5. 检查数据同步问题
        $issues = array_merge($issues, $this->checkDataSyncIssues($output));
        
        if (empty($issues)) {
            $output->writeln('<info>✅ 数据一致性检查通过，未发现问题！</info>');
        } else {
            $output->writeln('<error>❌ 发现 ' . count($issues) . ' 个数据一致性问题：</error>');
            foreach ($issues as $issue) {
                $output->writeln('<error>  - ' . $issue . '</error>');
            }
        }
        
        return 0;
    }
    
    /**
     * 检查学生表外键问题
     */
    private function checkStudentForeignKeys($output)
    {
        $issues = [];
        $output->writeln('检查学生表外键...');
        
        // 检查班级外键
        $invalidBanji = Db::table('zz_student')
            ->alias('s')
            ->leftJoin('zz_banji b', 's.zz_banji_id = b.id')
            ->where('s.zz_banji_id', '>', 0)
            ->where('b.id', 'null')
            ->count();
            
        if ($invalidBanji > 0) {
            $issues[] = "学生表中有 {$invalidBanji} 条记录的班级ID无效";
        }
        
        // 检查宿舍外键
        $invalidDorm = Db::table('zz_student')
            ->alias('s')
            ->leftJoin('zz_dorm d', 's.zz_dorm_id = d.id')
            ->where('s.zz_dorm_id', '>', 0)
            ->where('d.id', 'null')
            ->count();
            
        if ($invalidDorm > 0) {
            $issues[] = "学生表中有 {$invalidDorm} 条记录的宿舍ID无效";
        }
        
        return $issues;
    }
    
    /**
     * 检查随迁子女报名表外键问题
     */
    private function checkMigrantForeignKeys($output)
    {
        $issues = [];
        $output->writeln('检查随迁子女报名表外键...');
        
        // 检查学生外键
        $invalidStudent = Db::table('zz_migrant_student_application')
            ->alias('m')
            ->leftJoin('zz_student s', 'm.zz_student_id = s.id')
            ->where('m.zz_student_id', '>', 0)
            ->where('s.id', 'null')
            ->count();
            
        if ($invalidStudent > 0) {
            $issues[] = "随迁子女报名表中有 {$invalidStudent} 条记录的学生ID无效";
        }
        
        // 检查证书外键
        $invalidCertificate = Db::table('zz_migrant_student_application')
            ->alias('m')
            ->leftJoin('zz_student_certificate c', 'm.zz_student_certificate_id = c.id')
            ->where('m.zz_student_certificate_id', '>', 0)
            ->where('c.id', 'null')
            ->count();
            
        if ($invalidCertificate > 0) {
            $issues[] = "随迁子女报名表中有 {$invalidCertificate} 条记录的证书ID无效";
        }
        
        return $issues;
    }
    
    /**
     * 检查高考报名表外键问题
     */
    private function checkGaokaogdForeignKeys($output)
    {
        $issues = [];
        $output->writeln('检查高考报名表外键...');
        
        // 检查学生外键
        $invalidStudent = Db::table('zz_gaokao_apply_local')
            ->alias('g')
            ->leftJoin('zz_student s', 'g.zz_student_id = s.id')
            ->where('g.zz_student_id', '>', 0)
            ->where('s.id', 'null')
            ->count();

        if ($invalidStudent > 0) {
            $issues[] = "高考报名表中有 {$invalidStudent} 条记录的学生ID无效";
        }

        // 检查证书外键
        $invalidCertificate = Db::table('zz_gaokao_apply_local')
            ->alias('g')
            ->leftJoin('zz_student_certificate c', 'g.zz_student_certificate_id = c.id')
            ->where('g.zz_student_certificate_id', '>', 0)
            ->where('c.id', 'null')
            ->count();

        if ($invalidCertificate > 0) {
            $issues[] = "高考报名表中有 {$invalidCertificate} 条记录的证书ID无效";
        }
        
        return $issues;
    }
    
    /**
     * 检查证书表外键问题
     */
    private function checkCertificateForeignKeys($output)
    {
        $issues = [];
        $output->writeln('检查证书表外键...');
        
        // 检查学生外键
        $invalidStudent = Db::table('zz_student_certificate')
            ->alias('c')
            ->leftJoin('zz_student s', 'c.zz_student_id = s.id')
            ->where('c.zz_student_id', '>', 0)
            ->where('s.id', 'null')
            ->count();
            
        if ($invalidStudent > 0) {
            $issues[] = "证书表中有 {$invalidStudent} 条记录的学生ID无效";
        }
        
        return $issues;
    }
    
    /**
     * 检查数据同步问题
     */
    private function checkDataSyncIssues($output)
    {
        $issues = [];
        $output->writeln('检查数据同步问题...');
        
        // 检查学生信息与报名信息的同步
        $syncIssues = Db::query("
            SELECT COUNT(*) as count FROM zz_migrant_student_application m
            INNER JOIN zz_student s ON m.zz_student_id = s.id
            WHERE m.student_name != s.name OR m.student_id_card != s.id_card
        ");
        
        if ($syncIssues[0]['count'] > 0) {
            $issues[] = "随迁子女报名表中有 {$syncIssues[0]['count']} 条记录与学生表信息不同步";
        }
        
        return $issues;
    }
}

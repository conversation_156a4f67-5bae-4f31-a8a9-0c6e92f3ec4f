<?php

namespace app\admin\model;

use think\Model;


class Bjteacher extends Model
{

    

    

    // 表名
    protected $name = 'bjteacher';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function banji()
    {
        return $this->belongsTo('Banji', 'zz_banji_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function teacher()
    {
        return $this->belongsTo('Teacher', 'zz_teacher_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}

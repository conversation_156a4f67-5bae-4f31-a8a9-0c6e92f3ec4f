
<script>
    var getStudentInfoUrl = '{:url("gaokaogd/getStudentInfo")}';
</script>
<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <!-- 添加学号输入框 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('学生学号')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-student_no" class="form-control" name="row[student_no]" type="text" >
        </div>
    </div>

    <!-- 显示学生姓名 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('学生姓名')}:</label>
        <div class="col-xs-12 col-sm-8">
            <span id="student_name_display"></span>
        </div>
    </div>

    <!-- 显示学生班级 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('学生班级')}:</label>
        <div class="col-xs-12 col-sm-8">
            <span id="student_banji_display"></span>
        </div>
    </div>

    <!-- 显示学生身份证 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('学生身份证')}:</label>
        <div class="col-xs-12 col-sm-8">
            <span id="id_card_display"></span>
        </div>
    </div>

    <!-- 显示证书状态和刷新按钮 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('证书状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            <span id="certificate_status" style="font-weight: bold;"></span>
            <button type="button" id="refresh-certificate" class="btn btn-sm btn-info" style="margin-left: 10px;">
                <i class="fa fa-refresh"></i> 刷新证书信息
            </button>
        </div>
    </div>

    <!-- 隐藏的 student_id 输入框 -->
    <div class="form-group" style="display: none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_student_id')}:</label>
        <div class="col-xs-12 col-sm-8">
           <input id="c-zz_student_id" class="form-control" name="row[zz_student_id]" type="text">
         </div>
    </div>

    <!-- 隐藏的学生证书 ID 输入框 -->
    <div class="form-group" style="display: none;">
            <label class="control-label col-xs-12 col-sm-2">{:__('Zz_student_certificate_id')}:</label>
         <div class="col-xs-12 col-sm-8">
         <input id="c-zz_student_certificate_id" class="form-control" name="row[zz_student_certificate_id]" type="text">
        </div>
    </div>



    <!-- 移除学生 ID 输入框，或学生ID也应动态获取 -->
    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_student_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zz_student_id" data-rule="required" min="0" data-source="student/index" class="form-control selectpage" name="row[zz_student_id]" type="text" value="">
        </div>
    </div> -->
    <!-- 移除学生证书 ID 输入框 -->
    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_student_certificate_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zz_student_certificate_id" min="0" data-rule="required" data-source="Stucertifi/index" class="form-control selectpage" name="row[zz_student_certificate_id]" type="text" value="">
        </div>
    </div> -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Registration_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-registration_type" data-rule="required" class="form-control selectpicker" name="row[registration_type]">
                {foreach name="registrationTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Household_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-household_type" data-rule="required" class="form-control selectpicker" name="row[household_type]">
                {foreach name="householdTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Household_address_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-household_address_city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[household_address_city]" type="text"></div>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('School_roll_location')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-school_roll_location" data-rule="required" class="form-control" name="row[school_roll_location]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Full_three_years')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-full_three_years" data-rule="required" class="form-control selectpicker" name="row[full_three_years]">
                {foreach name="fullThreeYearsList" item="vo"}
                    <option value="{$key}" {in name="key" value="是"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('School_roll_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-school_roll_no" class="form-control" name="row[school_roll_no]" type="text">
        </div>
    </div>
    <!-- 隐藏原身份证输入框 -->
    <div class="form-group" style="display: none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('Id_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-id_card" class="form-control" name="row[id_card]" type="text">
        </div>
    </div>
    <div class="form-group" id="move_in_date_group" style="display: none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('Move_in_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-move_in_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[move_in_date]" type="text" value="{:date('Y-m-d')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
/* 优化表格列宽和显示效果 */
.bootstrap-table .fixed-table-header th {
    white-space: normal !important;
    word-wrap: break-word;
    line-height: 1.3;
    padding: 12px 6px !important;
    min-height: 60px !important;
    height: auto !important;
    vertical-align: middle !important;
    font-size: 12px;
}

/* 为是/否类型的列设置更小的宽度 */
.bootstrap-table .fixed-table-body td {
    white-space: normal !important;
    word-wrap: break-word;
    line-height: 1.4;
    padding: 8px 6px !important;
    vertical-align: middle !important;
    font-size: 12px;
}

/* 优化表头高度 */
.bootstrap-table .fixed-table-header {
    height: auto !important;
}

/* 确保表头文字完整显示 */
.bootstrap-table .fixed-table-header th .th-inner {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
    text-overflow: clip !important;
    height: auto !important;
    line-height: 1.3 !important;
}

/* 分组边框样式 */
.bootstrap-table .fixed-table-body td[style*="border-left"] {
    border-left-width: 3px !important;
    border-left-style: solid !important;
}

.bootstrap-table .fixed-table-body td[style*="border-right"] {
    border-right-width: 3px !important;
    border-right-style: solid !important;
}

/* 表头边框样式 */
.bootstrap-table .fixed-table-header th[style*="border-left"] {
    border-left-width: 3px !important;
    border-left-style: solid !important;
}

.bootstrap-table .fixed-table-header th[style*="border-right"] {
    border-right-width: 3px !important;
    border-right-style: solid !important;
}

/* Tooltip样式 */
.table-tooltip {
    position: relative;
    cursor: help;
}

.table-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.table-tooltip:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    z-index: 1000;
}
</style>

<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('migrant/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('migrant/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('migrant/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>

                        <a href="javascript:;" class="btn btn-warning btn-import {:$auth->check('migrant/import')?'':'hide'}" title="{:__('Import')}"><i class="fa fa-upload"></i> {:__('Import')}</a>

                        <a href="javascript:;" class="btn btn-info btn-sync-certificates {:$auth->check('migrant/synccertificates')?'':'hide'}" title="同步证书信息"><i class="fa fa-refresh"></i> 同步证书</a>

                        <a href="javascript:;" class="btn btn-success btn-batch-pass" title="批量通过选中项"><i class="fa fa-check"></i> 一键通过</a>

                        <a href="javascript:;" class="btn btn-danger btn-batch-reject" title="批量未通过选中项"><i class="fa fa-times"></i> 一键未通过</a>

                        <a class="btn btn-success btn-recyclebin btn-dialog {:$auth->check('migrant/recyclebin')?'':'hide'}" href="migrant/recyclebin" title="{:__('Recycle bin')}"><i class="fa fa-recycle"></i> {:__('Recycle bin')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('migrant/edit')}"
                           data-operate-del="{:$auth->check('migrant/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

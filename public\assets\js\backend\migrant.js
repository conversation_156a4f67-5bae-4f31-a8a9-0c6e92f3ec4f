define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'migrant/index' + location.search,
                    add_url: 'migrant/add',
                    edit_url: 'migrant/edit',
                    del_url: 'migrant/del',
                    multi_url: 'migrant/multi',
                    import_url: 'migrant/import',
                    table: 'migrant_student_application',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        // 第一组：基本信息（浅蓝色背景）
                        {field: 'application_category', title: __('Application_category'), searchList: {"3+证书":__('3+证书'),"三二分段":__('三二分段'),"五年一贯":__('五年一贯'),"其他":__('其他')}, formatter: Table.api.formatter.normal,
                            cellStyle: {css: {'background-color': '#f0f8ff', 'border-left': '3px solid #87ceeb'}},
                            titleTooltip: '报考类别'},
                        {field: 'class_name', title: __('Class_name'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0f8ff'}},
                            titleTooltip: '班级'},
                        {field: 'student_number', title: __('Student_number'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0f8ff'}},
                            titleTooltip: '学号'},
                        {field: 'student_name', title: __('Student_name'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0f8ff'}},
                            titleTooltip: '姓名'},
                        {field: 'student_id_card', title: __('Student_id_card'),
                            cellStyle: {css: {'background-color': '#f0f8ff'}},
                            titleTooltip: '身份证号'},
                        {field: 'student_household_type', title: __('Student_household_type'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0f8ff'}},
                            titleTooltip: '户口所在省市'},
                        {field: 'student.native_place_city', title: __('Student.native_place_city'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0f8ff'}},
                            titleTooltip: '籍贯'},
                        {field: 'student.household_register_city', title: __('Student.household_register_city'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content,
                            cellStyle: {css: {'background-color': '#f0f8ff'}},
                            titleTooltip: '户籍所在地'},
                        {field: 'student.status', title: __('Student.status'), formatter: Table.api.formatter.status,
                            cellStyle: {css: {'background-color': '#f0f8ff'}},
                            titleTooltip: '状态'},
                        {field: 'certificate.name', title: __('Certificate.name'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0f8ff', 'border-right': '3px solid #87ceeb'}},
                            titleTooltip: '证书名称'},

                        // 第二组：社保相关（浅绿色背景）
                        {field: 'is_same_household_type', title: __('Is_same_household_type'), searchList: {"是":__('是'),"否":__('否')}, formatter: Table.api.formatter.normal,
                            cellStyle: {css: {'background-color': '#f0fff0', 'border-left': '3px solid #90ee90'}}, width: 80,
                            titleTooltip: '户口本是否与社保送审人同本？'},
                        {field: 'social_security_contributor', title: __('Social_security_contributor'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0fff0'}},
                            titleTooltip: '社保送审人'},
                        {field: 'ss_contributor_household', title: __('Ss_contributor_household'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0fff0'}},
                            titleTooltip: '社保送审人户口所在省市'},
                        {field: 'ss_contributor_id_card', title: __('Ss_contributor_id_card'),
                            cellStyle: {css: {'background-color': '#f0fff0'}},
                            titleTooltip: '社保送审人身份证号'},
                        {field: 'ss_relationship_type', title: __('Ss_relationship_type'), searchList: {"父亲":__('父亲'),"母亲":__('母亲'),"其他法定监护人":__('其他法定监护人')}, formatter: Table.api.formatter.normal,
                            cellStyle: {css: {'background-color': '#f0fff0'}},
                            titleTooltip: '社保送审人与考生关系'},
                        {field: 'social_security_city', title: __('Social_security_city'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#f0fff0'}},
                            titleTooltip: '参保地市'},
                        {field: 'pension_insurance_months', title: __('Pension_insurance_months'),
                            cellStyle: {css: {'background-color': '#f0fff0'}},
                            titleTooltip: '参保月数（养老）'},
                        {field: 'medical_insurance_months', title: __('Medical_insurance_months'),
                            cellStyle: {css: {'background-color': '#f0fff0', 'border-right': '3px solid #90ee90'}},
                            titleTooltip: '参保月数（医疗）'},

                        // 第三组：居住证相关（浅黄色背景）
                        {field: 'ss_is_same_household_type', title: __('Ss_is_same_household_type'), searchList: {"是":__('是'),"否":__('否')}, formatter: Table.api.formatter.normal,
                            cellStyle: {css: {'background-color': '#fffef0', 'border-left': '3px solid #f0e68c'}}, width: 80,
                            titleTooltip: '户口本是否与居住证送审人同本？'},
                        {field: 'residence_permit_contributor', title: __('Residence_permit_contributor'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#fffef0'}},
                            titleTooltip: '居住证送审人'},
                        {field: 'rp_contributor_household', title: __('Rp_contributor_household'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#fffef0'}},
                            titleTooltip: '居住证送审人户口所在省市'},
                        {field: 'rp_contributor_id_card', title: __('Rp_contributor_id_card'),
                            cellStyle: {css: {'background-color': '#fffef0'}},
                            titleTooltip: '居住证送审人身份证号'},
                        {field: 'rp_relationship_type', title: __('Rp_relationship_type'), searchList: {"父亲":__('父亲'),"母亲":__('母亲'),"其他法定监护人":__('其他法定监护人')}, formatter: Table.api.formatter.normal,
                            cellStyle: {css: {'background-color': '#fffef0'}},
                            titleTooltip: '居住证送审人与考生关系'},
                        {field: 'residence_permit_city', title: __('Residence_permit_city'), operate: 'LIKE',
                            cellStyle: {css: {'background-color': '#fffef0'}},
                            titleTooltip: '办理地市'},
                        {field: 'residence_permit_start_date', title: __('Residence_permit_start_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false,
                            cellStyle: {css: {'background-color': '#fffef0'}},
                            titleTooltip: '初办时间'},
                        {field: 'is_residence_permit_valid', title: __('Is_residence_permit_valid'), searchList: {"是":__('是'),"否":__('否')}, formatter: Table.api.formatter.normal,
                            cellStyle: {css: {'background-color': '#fffef0', 'border-right': '3px solid #f0e68c'}}, width: 80,
                            titleTooltip: '目前是否有效状态？'},

                        // 其他列（保持原样，优化宽度）
                        {field: 'has_three_year_school_roll', title: __('Has_three_year_school_roll'), searchList: {"是":__('是'),"否":__('否')}, formatter: Table.api.formatter.normal, width: 80,
                            titleTooltip: '是否具有三年完整学籍'},
                        {field: 'school_roll_no', title: __('School_roll_no'), operate: 'LIKE',
                            titleTooltip: '学籍号'},
                        {field: 'school_review_status_type', title: __('School_review_status_type'), searchList: {"未审查":__('未审查'),"通过":__('通过'),"未通过":__('未通过')},
                            formatter: function(value, row, index) {
                                var colorClass = '';
                                switch(value) {
                                    case '未审查': colorClass = 'text-muted'; break;
                                    case '通过': colorClass = 'text-success'; break;
                                    case '未通过': colorClass = 'text-danger'; break;
                                }
                                return '<span class="' + colorClass + '">' + value + '</span>';
                            },
                            titleTooltip: '学校审查状态类型'
                        },
                        {field: 'review_opinion', title: __('Review_opinion'), operate: 'LIKE',
                            titleTooltip: '审查意见'},
                        {field: 'has_certificate', title: __('Has_certificate'), searchList: {"是":__('是'),"否":__('否')}, formatter: Table.api.formatter.normal, width: 80,
                            titleTooltip: '是否有证书'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // 关联字段已移到第一组，这里不再重复显示
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'edit',
                                    text: __('Edit'),
                                    title: __('Edit'),
                                    classname: 'btn btn-xs btn-success btn-editone',
                                    icon: 'fa fa-edit',
                                    url: 'migrant/edit'
                                },
                                {
                                    name: 'del',
                                    text: __('Del'),
                                    title: __('Del'),
                                    classname: 'btn btn-xs btn-danger btn-delone',
                                    icon: 'fa fa-trash',
                                    url: 'migrant/del'
                                },
                                {
                                    name: 'review',
                                    text: '审查',
                                    title: '审查申请',
                                    classname: 'btn btn-xs btn-warning btn-review btn-click',
                                    icon: 'fa fa-check-circle',
                                    click: function(options, row, button) {
                                        Controller.api.showReviewDialog(row);
                                    }
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 初始化表头tooltip
            setTimeout(function() {
                Controller.api.initHeaderTooltips();
            }, 500);

            // 绑定全局同步证书按钮事件
            $('.btn-sync-certificates').on('click', function() {
                Controller.api.syncAllCertificates();
            });

            // 绑定批量通过按钮事件
            $('.btn-batch-pass').on('click', function() {
                Controller.api.batchReview('通过');
            });

            // 绑定批量未通过按钮事件
            $('.btn-batch-reject').on('click', function() {
                Controller.api.batchReview('未通过');
            });

            // 绑定批量未通过按钮事件
            $('.btn-batch-reject').on('click', function() {
                Controller.api.batchReview('未通过');
            });

            // 绑定导入按钮事件 - 使用FastAdmin标准方式
            $(document).on('click', '.btn-import', function () {
                var that = this;
                var options = {
                    url: 'migrant/import',
                    mimetype: 'csv,xls,xlsx',
                    multiple: false
                };
                Fast.api.upload(options, function (data, ret) {
                    Fast.api.ajax({
                        url: 'migrant/import',
                        data: {file: data.url}
                    }, function (data, ret) {
                        layer.msg(__('Import successful'));
                        $('#table').bootstrapTable('refresh');
                    });
                });
            });
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'migrant/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '140px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'migrant/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'migrant/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));

                // 绑定编辑页面的刷新证书按钮
                $('.btn-refresh-certificate-edit').on('click', function() {
                    var id = $('input[name="row[id]"]').val();
                    if (id) {
                        Controller.api.refreshEditPageCertificate(id);
                    } else {
                        layer.msg('请先保存记录后再刷新证书信息', {icon: 2});
                    }
                });
            },

            // 全局同步证书信息
            syncAllCertificates: function() {
                layer.confirm('确定要同步所有学生的证书信息吗？', {
                    title: '确认同步',
                    icon: 3
                }, function(index) {
                    layer.close(index);
                    var loading = layer.load(1, {shade: [0.3, '#000']});

                    $.ajax({
                        url: 'migrant/synccertificates',
                        method: 'POST',
                        data: {},
                        success: function(response) {
                            layer.close(loading);
                            if (response.code === 1) {
                                layer.msg('证书信息同步成功', {icon: 1});
                                $('#table').bootstrapTable('refresh');
                            } else {
                                layer.msg(response.msg || '同步失败', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.close(loading);
                            layer.msg('同步失败，请重试', {icon: 2});
                        }
                    });
                });
            },

            // 单个记录刷新证书信息
            refreshSingleCertificate: function(id) {
                var loading = layer.load(1, {shade: [0.3, '#000']});

                $.ajax({
                    url: 'migrant/refreshcertificate',
                    method: 'POST',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        layer.close(loading);
                        if (response.code === 1) {
                            layer.msg('证书信息已刷新', {icon: 1});
                            $('#table').bootstrapTable('refresh');
                        } else {
                            layer.msg(response.msg || '刷新失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loading);
                        layer.msg('刷新失败，请重试', {icon: 2});
                    }
                });
            },

            // 编辑页面刷新证书信息
            refreshEditPageCertificate: function(id) {
                var loading = layer.load(1, {shade: [0.3, '#000']});

                $.ajax({
                    url: 'migrant/refreshcertificate',
                    method: 'POST',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        layer.close(loading);
                        if (response.code === 1) {
                            layer.msg('证书信息已刷新', {icon: 1});
                            // 刷新页面以显示最新的证书信息
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            layer.msg(response.msg || '刷新失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loading);
                        layer.msg('刷新失败，请重试', {icon: 2});
                    }
                });
            },

            // 显示审查对话框
            showReviewDialog: function(row) {
                var html = '<div class="form-group">' +
                    '<label>审查状态：</label>' +
                    '<select id="review-status" class="form-control">' +
                    '<option value="未审查"' + (row.school_review_status_type === '未审查' ? ' selected' : '') + '>未审查</option>' +
                    '<option value="通过"' + (row.school_review_status_type === '通过' ? ' selected' : '') + '>通过</option>' +
                    '<option value="未通过"' + (row.school_review_status_type === '未通过' ? ' selected' : '') + '>未通过</option>' +
                    '</select>' +
                    '</div>' +
                    '<div class="form-group" id="opinion-group" style="display:none;">' +
                    '<label>未通过原因：</label>' +
                    '<textarea id="review-opinion" class="form-control" rows="3" placeholder="请填写未通过原因">' + (row.review_opinion || '') + '</textarea>' +
                    '</div>';

                var dialogIndex = layer.open({
                    type: 1,
                    title: '审查申请 - ' + row.student_name,
                    content: html,
                    area: ['400px', '300px'],
                    success: function(layero, index) {
                        // 监听状态变化
                        layero.find('#review-status').on('change', function() {
                            if ($(this).val() === '未通过') {
                                layero.find('#opinion-group').show();
                                layero.find('#review-opinion').focus(); // 自动聚焦到文本框
                            } else {
                                layero.find('#opinion-group').hide();
                            }
                        });

                        // 初始化显示
                        if (layero.find('#review-status').val() === '未通过') {
                            layero.find('#opinion-group').show();
                        }

                        // 监听回车键提交
                        layero.find('#review-opinion').on('keydown', function(e) {
                            if (e.keyCode === 13 && e.ctrlKey) { // Ctrl+Enter提交
                                layero.find('.layui-layer-btn0').click();
                            }
                        });
                    },
                    btn: ['确定', '取消'],
                    yes: function(index, layero) {
                        var status = layero.find('#review-status').val();
                        var opinion = layero.find('#review-opinion').val();

                        if (status === '未通过' && !opinion.trim()) {
                            layer.msg('请填写未通过原因', {icon: 2});
                            return false;
                        }

                        // 修改updateReviewStatus方法，传递弹框索引以便自动关闭
                        Controller.api.updateReviewStatus([row.id], status, opinion, index);
                        return false; // 阻止默认关闭，让updateReviewStatus控制关闭时机
                    }
                });
            },

            // 批量审查
            batchReview: function(status) {
                var ids = Table.api.selectedids($('#table'));
                if (ids.length === 0) {
                    layer.msg('请先选择要操作的记录', {icon: 2});
                    return;
                }

                if (status === '未通过') {
                    // 未通过需要填写统一原因
                    layer.prompt({
                        title: '批量设为未通过',
                        formType: 2,
                        value: '',
                        area: ['400px', '200px']
                    }, function(opinion, index) {
                        if (!opinion.trim()) {
                            layer.msg('请填写未通过原因', {icon: 2});
                            return false;
                        }

                        // 发送AJAX请求
                        $.post('migrant/batchreview', {
                            'ids': ids.join(','),
                            'status': status,
                            'opinion': opinion
                        }, function(response) {
                            if (response.code == 1) {
                                layer.msg(response.msg || '操作成功', {icon: 1});
                                $('#table').bootstrapTable('refresh');
                            } else {
                                layer.msg(response.msg || '操作失败', {icon: 2});
                            }
                        }, 'json').fail(function(xhr) {
                            layer.msg('操作失败，请重试', {icon: 2});
                        });
                    });
                } else {
                    // 通过直接执行
                    layer.confirm('确定要将选中的 ' + ids.length + ' 条记录设为通过吗？', function(index) {
                        Controller.api.updateReviewStatus(ids, status, '');
                        layer.close(index);
                    });
                }
            },

            // 更新审查状态
            updateReviewStatus: function(ids, status, opinion, dialogIndex) {
                // 确保ids是数组格式
                if (typeof ids === 'string') {
                    ids = ids.split(',');
                }

                var loading = layer.load(2, {shade: [0.3, '#fff']});

                $.post('migrant/batchreview', {
                    'ids': ids.join(','),  // 发送逗号分隔的字符串
                    'status': status,
                    'opinion': opinion
                }, function(response) {
                    layer.close(loading);
                    if (response.code == 1) {
                        layer.msg(response.msg || '操作成功', {icon: 1});
                        $('#table').bootstrapTable('refresh');

                        // 如果有弹框索引，自动关闭弹框
                        if (dialogIndex) {
                            layer.close(dialogIndex);
                        }
                    } else {
                        layer.msg(response.msg || '操作失败', {icon: 2});
                        // 失败时也要关闭弹窗
                        if (dialogIndex) {
                            layer.close(dialogIndex);
                        }
                    }
                }, 'json').fail(function(xhr) {
                    layer.close(loading);
                    layer.msg('操作失败，请重试', {icon: 2});
                    // 失败时也要关闭弹窗
                    if (dialogIndex) {
                        layer.close(dialogIndex);
                    }
                });
            },

            // 初始化表头tooltip
            initHeaderTooltips: function() {
                // 定义列名和tooltip的映射
                var tooltipMap = {
                    'application_category': '报考类别',
                    'class_name': '班级',
                    'student_number': '学号',
                    'student_name': '姓名',
                    'student_id_card': '身份证号',
                    'student_household_type': '户口所在省市',
                    'student.native_place_city': '籍贯',
                    'student.household_register_city': '户籍所在地',
                    'student.status': '状态',
                    'certificate.name': '证书名称',
                    'is_same_household_type': '户口本是否与社保送审人同本？',
                    'social_security_contributor': '社保送审人',
                    'ss_contributor_household': '社保送审人户口所在省市',
                    'ss_contributor_id_card': '社保送审人身份证号',
                    'ss_relationship_type': '社保送审人与考生关系',
                    'social_security_city': '参保地市',
                    'pension_insurance_months': '参保月数（养老）',
                    'medical_insurance_months': '参保月数（医疗）',
                    'ss_is_same_household_type': '户口本是否与居住证送审人同本？',
                    'residence_permit_contributor': '居住证送审人',
                    'rp_contributor_household': '居住证送审人户口所在省市',
                    'rp_contributor_id_card': '居住证送审人身份证号',
                    'rp_relationship_type': '居住证送审人与考生关系',
                    'residence_permit_city': '办理地市',
                    'residence_permit_start_date': '初办时间',
                    'is_residence_permit_valid': '目前是否有效状态？',
                    'has_three_year_school_roll': '是否具有三年完整学籍',
                    'school_roll_no': '学籍号',
                    'school_review_status_type': '学校审查状态类型',
                    'review_opinion': '审查意见',
                    'has_certificate': '是否有证书'
                };

                // 为表头添加tooltip
                $('#table th').each(function() {
                    var $th = $(this);
                    var fieldName = $th.attr('data-field');
                    if (fieldName && tooltipMap[fieldName]) {
                        $th.attr('title', tooltipMap[fieldName]);
                        $th.addClass('table-tooltip');
                        $th.attr('data-tooltip', tooltipMap[fieldName]);
                    }
                });
            }
        }
    };
    return Controller;
});

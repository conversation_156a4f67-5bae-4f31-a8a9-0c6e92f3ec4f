define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'gkam_soc/index' + location.search,
                    add_url: 'gkam_soc/add',
                    edit_url: 'gkam_soc/edit',
                    del_url: 'gkam_soc/del',
                    multi_url: 'gkam_soc/multi',
                    import_url: 'gkam_soc/import',
                    table: 'gk_apply_migrant_social',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'apply_id', title: __('Apply_id')},
                        {field: 'guardian_name', title: __('Guardian_name'), operate: 'LIKE'},
                        {field: 'guardian_id_card', title: __('Guardian_id_card')},
                        {field: 'guardian_relation', title: __('Guardian_relation'), operate: 'LIKE'},
                        {field: 'insurance_city', title: __('Insurance_city'), operate: 'LIKE'},
                        {field: 'pension_months', title: __('Pension_months')},
                        {field: 'medical_months', title: __('Medical_months')},
                        {field: 'household_same', title: __('Household_same')},
                        {field: 'migrant.id', title: __('Migrant.id')},
                        {field: 'migrant.student_no', title: __('Migrant.student_no'), operate: 'LIKE'},
                        {field: 'migrant.name', title: __('Migrant.name'), operate: 'LIKE'},
                        {field: 'migrant.id_card', title: __('Migrant.id_card')},
                        {field: 'migrant.household_province', title: __('Migrant.household_province'), operate: 'LIKE'},
                        {field: 'migrant.class', title: __('Migrant.class'), operate: 'LIKE'},
                        {field: 'migrant.has_three_year_education', title: __('Migrant.has_three_year_education')},
                        {field: 'migrant.school_approval', title: __('Migrant.school_approval'), operate: 'LIKE'},
                        {field: 'migrant.certificate_obtained', title: __('Migrant.certificate_obtained')},
                        {field: 'migrant.status', title: __('Migrant.status')},
                        {field: 'migrant.create_time', title: __('Migrant.create_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'migrant.update_time', title: __('Migrant.update_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'migrant.class_teacher_approver', title: __('Migrant.class_teacher_approver'), operate: 'LIKE'},
                        {field: 'migrant.class_teacher_approve_time', title: __('Migrant.class_teacher_approve_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'migrant.dean_approver', title: __('Migrant.dean_approver'), operate: 'LIKE'},
                        {field: 'migrant.dean_approve_time', title: __('Migrant.dean_approve_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

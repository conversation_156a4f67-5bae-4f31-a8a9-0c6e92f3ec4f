<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 分析高考报名数据状态
 */
class AnalyzeGaokaogdData extends Command
{
    protected function configure()
    {
        $this->setName('analyze:gaokaogd-data')
            ->setDescription('分析高考报名数据的关联状态');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始分析高考报名数据状态...');
        
        // 1. 统计总数据量
        $totalCount = Db::table('zz_gaokao_apply_local')->count();
        $output->writeln("📊 高考报名总记录数：{$totalCount}");
        
        if ($totalCount == 0) {
            $output->writeln('<info>暂无高考报名数据</info>');
            return 0;
        }
        
        // 2. 分析学生关联状态
        $this->analyzeStudentRelation($output);
        
        // 3. 分析证书关联状态
        $this->analyzeCertificateRelation($output);
        
        // 4. 提供数据修复建议
        $this->provideSuggestions($output);
        
        return 0;
    }
    
    /**
     * 分析学生关联状态
     */
    private function analyzeStudentRelation($output)
    {
        $output->writeln("\n🔍 分析学生关联状态：");
        
        // 有学生ID的记录
        $withStudentId = Db::table('zz_gaokao_apply_local')
            ->where('zz_student_id', 'not null')
            ->count();
        $output->writeln("  - 已关联学生ID的记录：{$withStudentId}");
        
        // 无学生ID的记录
        $withoutStudentId = Db::table('zz_gaokao_apply_local')
            ->where('zz_student_id', 'null')
            ->count();
        $output->writeln("  - 未关联学生ID的记录：{$withoutStudentId}");
        
        // 检查关联的有效性
        $validRelations = Db::query("
            SELECT COUNT(*) as count FROM zz_gaokao_apply_local g
            INNER JOIN zz_student s ON g.zz_student_id = s.id
        ");
        $output->writeln("  - 有效学生关联：{$validRelations[0]['count']}");
        
        // 显示一些示例数据
        $samples = Db::table('zz_gaokao_apply_local')
            ->field('id, zz_student_id, id_card')
            ->limit(3)
            ->select();
            
        $output->writeln("  - 示例数据：");
        foreach ($samples as $sample) {
            $studentId = $sample['zz_student_id'] ?? 'NULL';
            $output->writeln("    ID:{$sample['id']}, 学生ID:{$studentId}, 身份证:{$sample['id_card']}");
        }
    }
    
    /**
     * 分析证书关联状态
     */
    private function analyzeCertificateRelation($output)
    {
        $output->writeln("\n📜 分析证书关联状态：");
        
        // 有证书ID的记录
        $withCertificateId = Db::table('zz_gaokao_apply_local')
            ->where('zz_student_certificate_id', 'not null')
            ->count();
        $output->writeln("  - 已关联证书ID的记录：{$withCertificateId}");
        
        // 无证书ID的记录
        $withoutCertificateId = Db::table('zz_gaokao_apply_local')
            ->where('zz_student_certificate_id', 'null')
            ->count();
        $output->writeln("  - 未关联证书ID的记录：{$withoutCertificateId}");
        
        // 检查关联的有效性
        $validCertRelations = Db::query("
            SELECT COUNT(*) as count FROM zz_gaokao_apply_local g
            INNER JOIN zz_student_certificate c ON g.zz_student_certificate_id = c.id
        ");
        $output->writeln("  - 有效证书关联：{$validCertRelations[0]['count']}");
    }
    
    /**
     * 提供修复建议
     */
    private function provideSuggestions($output)
    {
        $output->writeln("\n💡 数据修复建议：");
        
        // 检查是否可以通过身份证号关联学生
        $canLinkByIdCard = Db::query("
            SELECT COUNT(*) as count FROM zz_gaokao_apply_local g
            INNER JOIN zz_student s ON g.id_card = s.id_card
            WHERE g.zz_student_id IS NULL
        ");
        
        if ($canLinkByIdCard[0]['count'] > 0) {
            $output->writeln("  ✅ 可通过身份证号关联 {$canLinkByIdCard[0]['count']} 条学生记录");
            $output->writeln("     执行命令：php think fix:gaokaogd-relations");
        }
        
        // 检查学生表数据
        $studentCount = Db::table('zz_student')->count();
        $output->writeln("  📚 学生表总记录数：{$studentCount}");
        
        // 检查证书表数据
        $certificateCount = Db::table('zz_student_certificate')->count();
        $output->writeln("  📜 证书表总记录数：{$certificateCount}");
        
        $output->writeln("\n🎯 推荐操作：");
        $output->writeln("  1. 如果学生数据完整，执行关联修复");
        $output->writeln("  2. 如果学生数据不完整，先导入学生数据");
        $output->writeln("  3. 证书数据可以后续补充");
    }
}

<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 测试数据格式命令
 * 用于测试修复后的数据格式是否正确
 */
class TestDataFormat extends Command
{
    protected function configure()
    {
        $this->setName('test:data-format')
            ->setDescription('测试修复后的数据格式是否正确');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始测试数据格式...');
        
        // 测试高考报名数据格式
        $this->testGaokaogdFormat($output);
        
        // 测试随迁子女报名数据格式
        $this->testMigrantFormat($output);
        
        $output->writeln('<info>✅ 数据格式测试完成！</info>');
        
        return 0;
    }
    
    /**
     * 测试高考报名数据格式
     */
    private function testGaokaogdFormat($output)
    {
        $output->writeln('测试高考报名数据格式...');
        
        try {
            // 模拟控制器的查询逻辑
            $list = Db::table('zz_gaokao_apply_local')
                ->limit(1)
                ->select();
                
            if (empty($list)) {
                $output->writeln('  - 高考报名表暂无数据');
                return;
            }
            
            $row = $list[0];
            $output->writeln('  - 原始数据格式：' . (is_array($row) ? 'Array' : 'Object'));
            $output->writeln('  - 包含字段：' . implode(', ', array_keys($row)));
            
            // 测试外键字段
            if (isset($row['zz_student_id'])) {
                $output->writeln('  - 学生ID字段：' . ($row['zz_student_id'] === null ? 'NULL' : $row['zz_student_id']));
            }
            
            if (isset($row['zz_student_certificate_id'])) {
                $output->writeln('  - 证书ID字段：' . ($row['zz_student_certificate_id'] === null ? 'NULL' : $row['zz_student_certificate_id']));
            }
            
        } catch (\Exception $e) {
            $output->writeln('<error>  - 高考报名数据格式测试失败：' . $e->getMessage() . '</error>');
        }
    }
    
    /**
     * 测试随迁子女报名数据格式
     */
    private function testMigrantFormat($output)
    {
        $output->writeln('测试随迁子女报名数据格式...');
        
        try {
            // 模拟控制器的查询逻辑
            $list = Db::table('zz_migrant_student_application')
                ->limit(1)
                ->select();
                
            if (empty($list)) {
                $output->writeln('  - 随迁子女报名表暂无数据');
                return;
            }
            
            $row = $list[0];
            $output->writeln('  - 原始数据格式：' . (is_array($row) ? 'Array' : 'Object'));
            $output->writeln('  - 包含字段：' . implode(', ', array_keys($row)));
            
            // 测试外键字段
            if (isset($row['zz_student_id'])) {
                $output->writeln('  - 学生ID字段：' . ($row['zz_student_id'] === null ? 'NULL' : $row['zz_student_id']));
            }
            
            if (isset($row['zz_student_certificate_id'])) {
                $output->writeln('  - 证书ID字段：' . ($row['zz_student_certificate_id'] === null ? 'NULL' : $row['zz_student_certificate_id']));
            }
            
        } catch (\Exception $e) {
            $output->writeln('<error>  - 随迁子女报名数据格式测试失败：' . $e->getMessage() . '</error>');
        }
    }
}

<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 学生管理
 */
class Student extends Backend
{
    /**
     * Student模型对象
     * @var \app\admin\model\Student
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Student;
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['banji', 'dorm'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','student_no','id_card','name','gender','phone','father_phone','mother_phone','native_place_city','household_register_city','nation','political_status','address','status','zz_banji_id','zz_dorm_id','banji','dorm']);
                if ($row->banji) {
                    $row->getRelation('banji')->visible(['name']);
                }
                if ($row->dorm) {
                    $row->getRelation('dorm')->visible(['name']);
                }
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        // 传递状态列表给视图
        $this->view->assign("statusList", $this->model->getStatusList());
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        // 传递状态列表和性别列表给视图
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("genderList", $this->model->getGenderList());
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        // 传递状态列表和性别列表给视图
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("genderList", $this->model->getGenderList());
        return parent::edit($ids);
    }

    /**
     * 导入
     */
    public function import()
    {
        // 简化的导入逻辑，类似班级导入的简单版本
        $this->importCallback = function ($row) {
            // 检查是否为空行
            if (empty(trim($row['student_no'] ?? '')) && empty(trim($row['name'] ?? ''))) {
                return null; // 跳过空行
            }

            // 基本验证
            if (empty(trim($row['student_no'] ?? ''))) {
                throw new \Exception("学号不能为空");
            }
            if (empty(trim($row['name'] ?? ''))) {
                throw new \Exception("学生姓名不能为空");
            }

            // 核心修复：外键字段使用null而不是0
            if (empty($row['zz_banji_id'])) {
                $row['zz_banji_id'] = null;
            }
            if (empty($row['zz_dorm_id'])) {
                $row['zz_dorm_id'] = null;
            }

            return $row;
        };

        return parent::import();
    }

    /**
     * 根据学号获取学生信息
     * 用于证书添加页面的学号查询功能
     */
    public function getstudentinfo()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }

        $studentNo = $this->request->post('student_no');
        if (empty($studentNo)) {
            $this->error('学号不能为空');
        }

        // 查找学生信息
        $student = $this->model->where('student_no', $studentNo)->find();

        if ($student) {
            // 获取班级信息
            $banjiName = '';
            if ($student->zz_banji_id) {
                $banji = \app\admin\model\Banji::find($student->zz_banji_id);
                $banjiName = $banji ? $banji->name : '';
            }

            $this->success('查询成功', null, [
                'student_id' => $student->id,
                'name' => $student->name,
                'id_card' => $student->id_card,
                'banji_name' => $banjiName,
                'gender_text' => $student->gender_text,
                'status_text' => $student->status_text
            ]);
        } else {
            $this->error('未找到该学号对应的学生');
        }
    }
}

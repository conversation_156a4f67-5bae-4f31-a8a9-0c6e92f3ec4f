<?php

namespace app\admin\model;

use think\Model;
use traits\model\SoftDelete;

class Gaokaogd extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'gaokao_apply_local';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'registration_type_text',
        'household_type_text',
        'full_three_years_text'
    ];
    

    
    public function getRegistrationTypeList()
    {
        return ['3+证书' => __('3+证书'), '三二分段' => __('三二分段'), '五年一贯' => __('五年一贯'), '其他' => __('其他')];
    }

    public function getHouseholdTypeList()
    {
        return ['居民' => __('居民'), '农村' => __('农村')];
    }

    public function getFullThreeYearsList()
    {
        return ['是' => __('是'), '否' => __('否')];
    }


    public function getRegistrationTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['registration_type']) ? $data['registration_type'] : '');
        $list = $this->getRegistrationTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getHouseholdTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['household_type']) ? $data['household_type'] : '');
        $list = $this->getHouseholdTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getFullThreeYearsTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['full_three_years']) ? $data['full_three_years'] : '');
        $list = $this->getFullThreeYearsList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function student()
    {
        return $this->belongsTo('Student', 'zz_student_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function certificate()
    {
        return $this->belongsTo('Stucertifi', 'zz_student_certificate_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


}

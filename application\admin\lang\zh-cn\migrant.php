<?php

return [
    'Zz_student_id'                   => '学生ID',
    'Zz_student_certificate_id'       => '学生证书ID',
    'Application_category'            => '报考类别',
    'Class_name'                      => '班级',
    'Student_number'                  => '学号',
    'Student_name'                    => '姓名',
    'Student_id_card'                 => '身份证号',
    'Student_household_type'          => '户口所在省市',
    'Is_same_household_type'          => '户口本是否与社保送审人同本？',
    'Social_security_contributor'     => '社保送审人',
    'Ss_contributor_household'        => '社保送审人户口所在省市',
    'Ss_contributor_id_card'          => '社保送审人身份证号',
    'Ss_relationship_type'            => '社保送审人与考生关系',
    'Social_security_city'            => '参保地市',
    'Pension_insurance_months'        => '参保月数（养老）',
    'Medical_insurance_months'        => '参保月数（医疗）',
    'Ss_is_same_household_type'       => '户口本是否与居住证送审人同本？',
    'Residence_permit_contributor'    => '居住证送审人',
    'Rp_contributor_household'        => '居住证送审人户口所在省市',
    'Rp_contributor_id_card'          => '居住证送审人身份证号',
    'Rp_relationship_type'            => '居住证送审人与考生关系',
    'Residence_permit_city'           => '办理地市',
    'Residence_permit_start_date'     => '初办时间',
    'Is_residence_permit_valid'       => '目前是否有效状态？',
    'Has_three_year_school_roll'      => '是否有我省三年完整学籍？',
    'School_roll_no'                  => '学籍号(可空)',
    'School_review_status_type'       => '学校审查情况',
    'Review_opinion'                  => '审查不通过意见（仅当审查未通过时填写）',
    'Has_certificate'                 => '是否已考取证书',
    'Createtime'                      => '创建时间',
    'Updatetime'                      => '更新时间',
    'Deletetime'                      => '删除时间',
    'Student.student_no'              => '学号',
    'Student.id_card'                 => '身份证号',
    'Student.name'                    => '姓名',
    'Student.gender'                  => '性别(单选)',
    'Student.gender male'             => '男',
    'Student.gender female'           => '女',
    'Student.native_place_city'       => '籍贯',
    'Student.household_register_city' => '户籍所在地',
    'Student.status'                  => '状态',
    'Certificate.name'                => '证书名称'
];

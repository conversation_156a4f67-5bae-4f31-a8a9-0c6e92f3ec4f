<?php

namespace app\admin\controller;

use app\common\controller\Backend;

use app\admin\library\Auth;
use Exception;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\db\exception\BindParamException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\response\Json;

/**
 * 随迁子女高考报名审核管理
 *
 * @icon fa fa-circle-o
 */
class Migrant extends Backend
{

    /**
     * Migrant模型对象
     * @var \app\admin\model\Migrant
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Migrant;
        $this->view->assign("applicationCategoryList", $this->model->getApplicationCategoryList());
        $this->view->assign("isSameHouseholdTypeList", $this->model->getIsSameHouseholdTypeList());
        $this->view->assign("ssRelationshipTypeList", $this->model->getSsRelationshipTypeList());
        $this->view->assign("ssIsSameHouseholdTypeList", $this->model->getSsIsSameHouseholdTypeList());
        $this->view->assign("rpRelationshipTypeList", $this->model->getRpRelationshipTypeList());
        $this->view->assign("isResidencePermitValidList", $this->model->getIsResidencePermitValidList());
        $this->view->assign("hasThreeYearSchoolRollList", $this->model->getHasThreeYearSchoolRollList());
        $this->view->assign("schoolReviewStatusTypeList", $this->model->getSchoolReviewStatusTypeList());
        $this->view->assign("hasCertificateList", $this->model->getHasCertificateList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


   /**
     * 查看（随迁子女信息列表）
     * @auth ("查看随迁子女信息列表")  // 新增：基础方法的权限注解
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true; // 恢复关联查询
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 恢复模型关联查询
            $list = $this->model
                    ->with(['student','certificate'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            // 恢复原来的数据处理方式
            // 批量获取班级信息，避免N+1查询问题
            $studentIds = [];
            foreach ($list as $row) {
                if ($row->getRelation('student') && $row->getRelation('student')->zz_banji_id) {
                    $studentIds[] = $row->getRelation('student')->zz_banji_id;
                }
            }

            $studentIds = array_unique($studentIds);
            $banjiList = [];
            if (!empty($studentIds)) {
                $banjiList = \think\Db::table('zz_banji')
                    ->where('id', 'in', $studentIds)
                    ->column('name', 'id');
            }

            foreach ($list as $row) {
                $row->visible(['id','zz_student_id','zz_student_certificate_id','application_category','class_name','student_number','student_name','student_id_card','student_household_type','is_same_household_type','social_security_contributor','ss_contributor_household','ss_contributor_id_card','ss_relationship_type','social_security_city','pension_insurance_months','medical_insurance_months','ss_is_same_household_type','residence_permit_contributor','rp_contributor_household','rp_contributor_id_card','rp_relationship_type','residence_permit_city','residence_permit_start_date','is_residence_permit_valid','has_three_year_school_roll','school_roll_no','school_review_status_type','review_opinion','has_certificate','createtime','updatetime']);
                $row->visible(['student']);
                $row->getRelation('student')->visible(['student_no','name','id_card','gender','native_place_city','household_register_city','status']);
                $row->visible(['certificate']);
                $row->getRelation('certificate')->visible(['name']);

                // 添加班级名称
                if ($row->getRelation('student') && $row->getRelation('student')->zz_banji_id) {
                    $banjiId = $row->getRelation('student')->zz_banji_id;
                    $row->banji_name = isset($banjiList[$banjiId]) ? $banjiList[$banjiId] : '';
                } else {
                    $row->banji_name = '';
                }
            }

            // 恢复原来的返回方式
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 根据学号获取学生信息
     *
     * @return Json
     */
    public function getStudentInfo()
    {
        $studentNo = $this->request->post('student_no');
        if (!$studentNo) {
            return json(['code' => 0, 'msg' => '学号不能为空']);
        }

        $studentInfo = Db::table('zz_student')//使用数据表查询
        ->alias('s')
        ->join('zz_student_certificate sc', 's.id = sc.zz_student_id', 'LEFT')
        ->join('zz_banji b', 's.zz_banji_id = b.id', 'LEFT')
        ->where('s.student_no', $studentNo)
        ->field('s.id as zz_student_id, s.name, s.id_card, b.name as banji_name, sc.id as zz_student_certificate_id')
        ->find();

        if ($studentInfo) {
            return json(['code' => 1, 'data' => [
                'name' => $studentInfo['name'],
                'id_card' => $studentInfo['id_card'],
                'student_id' => $studentInfo['zz_student_id'],
                'banji_name' => $studentInfo['banji_name'],
                'certificate_id' => $studentInfo['zz_student_certificate_id']

            ]]);
        } else {
            return json(['code' => 0, 'msg' => '未找到该学号对应的学生信息']);
        }
    }

    /**
     * 全局同步证书信息
     */
    public function synccertificates()
    {
        try {
            // 获取所有随迁子女申请记录
            $migrantList = $this->model->select();
            $updateCount = 0;

            foreach ($migrantList as $migrant) {
                if ($migrant->zz_student_id && $migrant->zz_student_id > 0) {
                    // 查询该学生最新的证书信息
                    $certificate = \app\admin\model\Stucertifi::where('zz_student_id', $migrant->zz_student_id)
                        ->order('id desc')
                        ->find();

                    $newCertificateId = $certificate ? $certificate->id : 0;
                    $newHasCertificate = $certificate ? '是' : '否';

                    // 如果证书信息有变化，则更新
                    if ($migrant->zz_student_certificate_id != $newCertificateId || $migrant->has_certificate != $newHasCertificate) {
                        $migrant->zz_student_certificate_id = $newCertificateId;
                        $migrant->has_certificate = $newHasCertificate;
                        $result = $migrant->save();
                        if ($result !== false) {
                            $updateCount++;
                        }
                    }
                }
            }

            $this->success("同步完成，共更新了 {$updateCount} 条记录");
        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HTTP响应异常，这是框架正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            \think\Log::write('同步证书异常详情: ' . $e->getMessage() . ' 文件: ' . $e->getFile() . ' 行号: ' . $e->getLine() . ' 堆栈: ' . $e->getTraceAsString(), 'error');
            $this->error('同步失败：' . $e->getMessage());
        }
    }

    /**
     * 单个记录刷新证书信息
     */
    public function refreshcertificate()
    {
        $id = $this->request->post('id');
        if (!$id) {
            $this->error('参数错误');
        }

        try {
            $migrant = $this->model->find($id);
            if (!$migrant) {
                $this->error('记录不存在');
            }

            if ($migrant->zz_student_id && $migrant->zz_student_id > 0) {
                // 查询该学生最新的证书信息
                $certificate = \app\admin\model\Stucertifi::where('zz_student_id', $migrant->zz_student_id)
                    ->order('id desc')
                    ->find();

                $newCertificateId = $certificate ? $certificate->id : 0;
                $newHasCertificate = $certificate ? '是' : '否';

                $migrant->zz_student_certificate_id = $newCertificateId;
                $migrant->has_certificate = $newHasCertificate;
                $migrant->save();

                $this->success('证书信息已刷新');
            } else {
                $this->error('该记录没有关联的学生信息');
            }
        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HTTP响应异常，这是框架正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            \think\Log::write('刷新证书失败: ' . $e->getMessage() . ' 文件: ' . $e->getFile() . ' 行号: ' . $e->getLine(), 'error');
            $this->error('刷新失败：' . $e->getMessage());
        }
    }

    public function import()
    {
        // 如果是GET请求，显示导入页面
        if ($this->request->isGet()) {
            return parent::import();
        }

        // 如果是POST请求，使用增强的错误收集机制

        // 初始化计数器
        $this->importRowCount = 0;

        // 设置预处理回调来验证和处理数据
        $this->importCallback = function ($row, $index = 0) {
            $rowNumber = $index + 2; // Excel行号（从第2行开始，第1行是标题）
            $this->importRowCount++;

            // 检查是否为空行，如果是空行则直接跳过处理
            if (empty($row['student_number']) && empty($row['student_name']) && empty($row['application_category'])) {
                return null; // 返回null表示跳过这一行
            }

            try {
                // 验证必填字段
                if (empty($row['student_number'])) {
                    throw new \Exception("第{$rowNumber}行：学号不能为空");
                }
                if (empty($row['student_name'])) {
                    throw new \Exception("第{$rowNumber}行：学生姓名不能为空");
                }
                if (empty($row['application_category'])) {
                    throw new \Exception("第{$rowNumber}行：报考类别不能为空");
                }

                // 验证学号格式
                if (!empty($row['student_number']) && !is_numeric($row['student_number'])) {
                    throw new \Exception("第{$rowNumber}行：学号格式不正确，应为数字，当前值：{$row['student_number']}");
                }

                // 验证身份证号格式
                if (!empty($row['student_id_card'])) {
                    $idCard = trim($row['student_id_card']);
                    $idCardError = $this->validateIdCardFormat($idCard);
                    if ($idCardError) {
                        throw new \Exception("第{$rowNumber}行：{$idCardError}");
                    }
                }

                // 验证报考类别
                $allowedCategories = ['3+证书', '三二分段', '五年一贯', '其他'];
                if (!empty($row['application_category']) && !in_array($row['application_category'], $allowedCategories)) {
                    throw new \Exception("第{$rowNumber}行：报考类别【{$row['application_category']}】不支持，支持的类别：" . implode('、', $allowedCategories));
                }

                // 如果验证通过，继续处理其他逻辑
            } catch (\Exception $e) {
                // 直接抛出异常，让FastAdmin处理
                throw $e;
            }

            // 处理数字字段（预验证已通过，这里只做数据转换）
            $numberFields = ['pension_insurance_months', 'medical_insurance_months'];
            foreach ($numberFields as $field) {
                if (isset($row[$field]) && !empty($row[$field])) {
                    $numericValue = preg_replace('/[^0-9]/', '', $row[$field]);
                    $row[$field] = intval($numericValue);
                } else {
                    $row[$field] = 0;
                }
            }

            // 处理日期字段（预验证已通过，这里只做数据转换）
            if (isset($row['residence_permit_start_date']) && !empty($row['residence_permit_start_date'])) {
                $dateValue = $row['residence_permit_start_date'];
                $convertedDate = null;

                if (is_numeric($dateValue) && $dateValue > 1) {
                    $unixTimestamp = ($dateValue - 25569) * 86400;
                    $convertedDate = date('Y-m-d', $unixTimestamp);
                } elseif (strpos($dateValue, '/') !== false) {
                    $dateParts = explode('/', $dateValue);
                    if (count($dateParts) == 3) {
                        $year = intval($dateParts[0]);
                        $month = intval($dateParts[1]);
                        $day = intval($dateParts[2]);
                        $convertedDate = sprintf('%04d-%02d-%02d', $year, $month, $day);
                    }
                } else {
                    $timestamp = strtotime($dateValue);
                    if ($timestamp !== false) {
                        $convertedDate = date('Y-m-d', $timestamp);
                    }
                }

                if ($convertedDate) {
                    $row['residence_permit_start_date'] = $convertedDate;
                }
            }

            // 自动判断是否有证书：根据学号查询学生是否有证书信息
            if (isset($row['student_number']) && !empty($row['student_number'])) {
                // 根据学号查找学生ID
                $student = \think\Db::table('zz_student')
                    ->where('student_no', $row['student_number'])
                    ->find();

                if ($student) {
                    // 检查是否已存在该学生的随迁子女报名记录
                    $existing = \think\Db::table('zz_migrant_student_application')
                        ->where('zz_student_id', $student['id'])
                        ->find();

                    if ($existing) {
                        throw new \Exception("第{$rowNumber}行：学号【{$row['student_number']}】的学生【{$student['name']}】已存在随迁子女报名记录，请勿重复导入");
                    }

                    // 设置学生ID关联
                    $row['zz_student_id'] = $student['id'];
                } else {
                    // 学号对应的学生不存在，抛出错误
                    throw new \Exception("第{$rowNumber}行：学号【{$row['student_number']}】对应的学生不存在，请检查学号是否正确");
                }

                // 查询该学生是否有证书
                $certificate = \think\Db::table('zz_student_certificate')
                    ->where('zz_student_id', $student['id'])
                    ->order('id desc')
                    ->find();

                // 智能处理"是否已考取证书"字段
                // 1. 如果用户漏填，根据是否有证书记录自动判断
                // 2. 如果用户填"否"但实际有证书，自动更正为"是"
                // 3. 如果用户填"是"但没有证书，保持"是"（可能证书数据还未录入）
                $userHasCertificate = $row['has_certificate'] ?? '';
                if (empty($userHasCertificate)) {
                    // 用户漏填，根据实际情况自动判断
                    $row['has_certificate'] = $certificate ? '是' : '否';
                } elseif ($userHasCertificate == '否' && $certificate) {
                    // 用户填"否"但实际有证书，更正为"是"
                    $row['has_certificate'] = '是';
                }
                // 其他情况保持用户填写的值

                // 设置证书ID（如果没有证书，设置为null而不是0）
                $row['zz_student_certificate_id'] = $certificate ? $certificate['id'] : null;
            }

            // 调试：输出最终要保存的数据
            \think\Log::write('准备保存的数据: ' . json_encode($row, JSON_UNESCAPED_UNICODE), 'info');

            return $row;
        };

        try {
            try {
            $result = parent::import();
        } catch (\Exception $e) {
            // 捕获并分析数据库错误
            $errorMsg = $e->getMessage();
            \think\Log::write('导入错误详情: ' . $errorMsg, 'error');

            // 如果是唯一性约束错误，提供更详细的信息
            if (strpos($errorMsg, 'Duplicate entry') !== false || strpos($errorMsg, '已存在') !== false) {
                // 分析错误信息，找出具体是哪个字段
                if (strpos($errorMsg, 'zz_student_id') !== false) {
                    throw new \Exception('学生ID重复：该学生已存在随迁子女报名记录');
                } elseif (strpos($errorMsg, 'student_number') !== false) {
                    throw new \Exception('学号重复：该学号已存在报名记录');
                } elseif (strpos($errorMsg, 'student_id_card') !== false) {
                    throw new \Exception('身份证号重复：该身份证号已存在报名记录');
                } else {
                    throw new \Exception('数据重复：' . $errorMsg);
                }
            }

            // 其他错误直接抛出
            throw $e;
        }
        } catch (\Exception $e) {
            // 如果是我们的自定义错误页面异常
            if (strpos($e->getMessage(), 'CUSTOM_ERROR_PAGE:') === 0) {
                $errorHtml = substr($e->getMessage(), strlen('CUSTOM_ERROR_PAGE:'));
                // 直接输出HTML页面
                echo $errorHtml;
                exit;
            } else {
                // 其他异常正常抛出
                throw $e;
            }
        }

        // 导入成功后，检查并修复最新插入的数据
        if ($result['code'] == 1) {
            $this->fixImportedData();
        }

        // 导入成功后，同步所有证书信息（确保数据准确性）
        $this->syncAllCertificatesAfterImport();

        return $result;
    }

    /**
     * 生成友好的错误HTML
     */
    private function generateErrorHtml($errors)
    {
        $totalErrors = array_sum(array_map('count', $errors));
        $totalRows = count($errors);

        $html = '<div style="text-align: left; max-height: 400px; overflow-y: auto; padding: 15px; background: #fff;">';
        $html .= '<div style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; padding: 15px; margin: -15px -15px 15px -15px; text-align: center;">';
        $html .= '<h3 style="margin: 0; font-size: 18px;"><i class="fa fa-exclamation-triangle"></i> 数据导入失败</h3>';
        $html .= '<p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 13px;">发现 ' . $totalRows . ' 行数据有 ' . $totalErrors . ' 个错误</p>';
        $html .= '</div>';

        foreach ($errors as $rowNumber => $rowErrors) {
            $html .= '<div style="border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 10px; background: #f8d7da;">';
            $html .= '<div style="background: #dc3545; color: white; padding: 8px 12px; font-weight: bold; font-size: 13px;">';
            $html .= '<i class="fa fa-times-circle"></i> 第 ' . $rowNumber . ' 行 (' . count($rowErrors) . ' 个错误)';
            $html .= '</div>';
            $html .= '<div style="padding: 10px 12px;">';
            foreach ($rowErrors as $error) {
                $html .= '<div style="margin-bottom: 5px; padding: 5px 8px; background: #fff; border-left: 3px solid #dc3545; font-size: 12px; color: #721c24;">';
                $html .= '<i class="fa fa-exclamation-circle" style="color: #dc3545; margin-right: 5px;"></i>' . htmlspecialchars($error);
                $html .= '</div>';
            }
            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '<div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 12px; margin-top: 15px; color: #0c5460;">';
        $html .= '<h5 style="margin: 0 0 8px 0; font-size: 13px;"><i class="fa fa-lightbulb-o"></i> 修正建议</h5>';
        $html .= '<ul style="margin: 0; padding-left: 18px; font-size: 11px;">';
        $html .= '<li>身份证号格式：18位或15位，月份应为01-12，日期应为01-31</li>';
        $html .= '<li>学号必须为纯数字</li>';
        $html .= '<li>报考类别只能是：3+证书、三二分段、五年一贯、其他</li>';
        $html .= '<li>是否类型字段只能填写：是 或 否</li>';
        $html .= '<li>参保月数应为0-600之间的数字</li>';
        $html .= '<li>居住证初办时间不能是未来日期</li>';
        $html .= '</ul>';
        $html .= '</div>';

        $html .= '</div>';

        return $html;
    }

    /**
     * 生成自定义错误页面HTML
     */
    private function generateCustomErrorPage($errors)
    {
        $totalErrors = array_sum(array_map('count', $errors));
        $totalRows = count($errors);

        // 使用模板引擎生成HTML
        $this->view->assign('errors', $errors);
        $this->view->assign('total_rows', $totalRows);
        $this->view->assign('total_errors', $totalErrors);
        return $this->view->fetch('import_errors');
    }

    /**
     * 验证身份证号格式
     */
    private function validateIdCardFormat($idCard)
    {
        $isValid = false;
        $errorDetail = '';

        // 18位身份证验证
        if (strlen($idCard) == 18) {
            if (!preg_match('/^[1-9]\d{5}/', $idCard)) {
                $errorDetail = '前6位地区码格式错误';
            } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}/', $idCard)) {
                $errorDetail = '出生年份格式错误（应为18xx、19xx或20xx）';
            } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])/', $idCard)) {
                $errorDetail = '出生月份格式错误（应为01-12）';
            } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])/', $idCard)) {
                $errorDetail = '出生日期格式错误（应为01-31）';
            } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/', $idCard)) {
                $errorDetail = '最后4位格式错误';
            } else {
                $isValid = true;
            }
        }
        // 15位身份证验证
        elseif (strlen($idCard) == 15) {
            if (!preg_match('/^[1-9]\d{7}(0\d|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}$/', $idCard)) {
                $errorDetail = '15位身份证格式错误';
            } else {
                $isValid = true;
            }
        } else {
            $errorDetail = '长度错误（应为15位或18位）';
        }

        return $isValid ? null : "身份证号格式不正确，{$errorDetail}，当前值：{$idCard}";
    }






    /**
     * 验证日期格式
     */
    private function validateDateFormat($dateValue)
    {
        try {
            $originalValue = $dateValue;
            $convertedDate = null;

            // 如果是纯数字（Excel日期序列号）
            if (is_numeric($dateValue) && $dateValue > 1) {
                $unixTimestamp = ($dateValue - 25569) * 86400;
                $convertedDate = date('Y-m-d', $unixTimestamp);
            }
            // 如果是字符串格式的日期
            elseif (strpos($dateValue, '/') !== false) {
                $dateParts = explode('/', $dateValue);
                if (count($dateParts) == 3) {
                    $year = intval($dateParts[0]);
                    $month = intval($dateParts[1]);
                    $day = intval($dateParts[2]);

                    if (!checkdate($month, $day, $year)) {
                        return "居住证初办时间日期无效，当前值：{$originalValue}";
                    }

                    $convertedDate = sprintf('%04d-%02d-%02d', $year, $month, $day);
                }
            } else {
                $timestamp = strtotime($dateValue);
                if ($timestamp !== false) {
                    $convertedDate = date('Y-m-d', $timestamp);
                }
            }

            if ($convertedDate === null) {
                return "居住证初办时间格式不正确，当前值：{$originalValue}";
            }

            // 验证日期范围
            $dateTimestamp = strtotime($convertedDate);
            $currentTimestamp = time();
            $minTimestamp = strtotime('1990-01-01');

            if ($dateTimestamp > $currentTimestamp) {
                return "居住证初办时间不能是未来日期，当前值：{$convertedDate}";
            }

            if ($dateTimestamp < $minTimestamp) {
                return "居住证初办时间过早，应在1990年之后，当前值：{$convertedDate}";
            }

            return null;

        } catch (\Exception $e) {
            return "居住证初办时间格式转换失败，当前值：{$dateValue}";
        }
    }

    /**
     * 修复导入的数据
     */
    private function fixImportedData()
    {
        try {
            // 获取最近导入的记录（最近1分钟内创建的记录）
            $recentTime = time() - 60; // 1分钟前
            $migrantList = $this->model->where('createtime', '>', $recentTime)->select();

            foreach ($migrantList as $migrant) {
                // 如果主表字段为空但有学生ID，说明是刚导入的需要修复的数据
                if (empty($migrant->student_number) && $migrant->zz_student_id > 0) {
                    // 从学生表获取数据来填充主表字段
                    $student = \app\admin\model\Student::find($migrant->zz_student_id);
                    if ($student) {
                        $migrant->student_number = $student->student_no;
                        $migrant->student_name = $student->name;
                        $migrant->student_id_card = $student->id_card;
                        $migrant->save();
                    }
                }
            }
        } catch (\Exception $e) {
            \think\Log::write('修复导入数据失败: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * 导入后同步所有证书信息
     */
    private function syncAllCertificatesAfterImport()
    {
        try {
            // 获取最近导入的记录（最近5分钟内创建的记录）
            $recentTime = time() - 300; // 5分钟前
            $migrantList = $this->model->where('createtime', '>', $recentTime)->select();

            $updateCount = 0;
            foreach ($migrantList as $migrant) {
                if ($migrant->zz_student_id && $migrant->zz_student_id > 0) {
                    // 查询该学生最新的证书信息
                    $certificate = \app\admin\model\Stucertifi::where('zz_student_id', $migrant->zz_student_id)
                        ->order('id desc')
                        ->find();

                    $newCertificateId = $certificate ? $certificate->id : 0;
                    $newHasCertificate = $certificate ? '是' : '否';

                    // 如果证书信息有变化，则更新
                    if ($migrant->zz_student_certificate_id != $newCertificateId || $migrant->has_certificate != $newHasCertificate) {
                        $migrant->zz_student_certificate_id = $newCertificateId;
                        $migrant->has_certificate = $newHasCertificate;
                        $result = $migrant->save();
                        if ($result !== false) {
                            $updateCount++;
                        }
                    }
                }
            }

            \think\Log::write("导入后证书同步完成，更新了 {$updateCount} 条记录", 'info');
        } catch (\Exception $e) {
            \think\Log::write('导入后证书同步失败: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * 批量审查
     */
    public function batchreview()
    {
        if (!$this->request->isPost()) {
            $this->error('非法请求');
        }

        // 获取参数
        $ids = $this->request->post('ids', '');
        $status = $this->request->post('status', '');
        $opinion = $this->request->post('opinion', '');

        // 处理ids - 如果是逗号分隔的字符串，转换为数组
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }
        $ids = array_filter($ids);

        if (empty($ids)) {
            $this->error('请选择要操作的记录');
        }

        if (!in_array($status, ['未审查', '通过', '未通过'])) {
            $this->error('审查状态不正确');
        }

        if ($status === '未通过' && empty(trim($opinion))) {
            $this->error('未通过时必须填写原因');
        }

        $updateData = [
            'school_review_status_type' => $status,
            'review_opinion' => $status === '未通过' ? $opinion : '',
            'updatetime' => time()
        ];

        try {
            $result = $this->model->where('id', 'in', $ids)->update($updateData);
        } catch (\Exception $e) {
            $this->error('操作失败：' . $e->getMessage());
        }

        // 检查更新结果 - 只要不是false就认为成功
        if ($result !== false) {
            $this->success('操作成功，共更新 ' . count($ids) . ' 条记录');
        } else {
            $this->error('操作失败 - 数据库更新失败');
        }
    }

}

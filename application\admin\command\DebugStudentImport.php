<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 调试学生导入问题
 */
class DebugStudentImport extends Command
{
    protected function configure()
    {
        $this->setName('debug:student-import')
            ->setDescription('调试学生导入的字段映射问题');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('调试学生导入问题...');
        
        // 1. 检查字段映射配置
        $this->checkFieldMapping($output);
        
        // 2. 检查日志文件
        $this->checkImportLogs($output);
        
        // 3. 提供解决方案
        $this->provideSolutions($output);
        
        return 0;
    }
    
    /**
     * 检查字段映射配置
     */
    private function checkFieldMapping($output)
    {
        $output->writeln("\n📋 字段映射配置：");
        
        // 模拟Student控制器的字段映射
        $importFields = [
            'student_no'        => '学号',
            'id_card'           => '身份证号',
            'name'              => '姓名',
            'gender'            => '性别(单选)',
            'phone'             => '联系电话',
            'father_phone'      => '父亲电话',
            'mother_phone'      => '母亲电话',
            'native_place_city' => '籍贯',
            'household_register_city' => '户籍所在地',
            'nation'            => '民族',
            'political_status'  => '政治面貌',
            'address'           => '现居地址',
            'status'            => '状态',
            'zz_banji_id'       => '班级名称',
            'zz_dorm_id'        => '宿舍号'
        ];
        
        $output->writeln("原始映射（数据库字段 => Excel表头）：");
        foreach ($importFields as $field => $header) {
            $output->writeln("  {$field} => {$header}");
        }
        
        $output->writeln("\n翻转后映射（Excel表头 => 数据库字段）：");
        $flippedFields = array_flip($importFields);
        foreach ($flippedFields as $header => $field) {
            $output->writeln("  '{$header}' => {$field}");
        }
        
        $output->writeln("\n⚠️  常见问题：");
        $output->writeln("1. Excel表头必须完全匹配，包括空格和标点符号");
        $output->writeln("2. 如果Excel表头是'学号 '（有空格），会导致映射失败");
        $output->writeln("3. 如果Excel表头是'Student_no'，也会导致映射失败");
    }
    
    /**
     * 检查导入日志
     */
    private function checkImportLogs($output)
    {
        $output->writeln("\n📝 检查导入日志：");
        
        $logPath = ROOT_PATH . 'runtime/log';
        if (!is_dir($logPath)) {
            $output->writeln("  - 日志目录不存在：{$logPath}");
            return;
        }
        
        // 查找最新的日志文件
        $logFiles = glob($logPath . '/*.log');
        if (empty($logFiles)) {
            $output->writeln("  - 未找到日志文件");
            return;
        }
        
        // 获取最新的日志文件
        $latestLog = max($logFiles);
        $output->writeln("  - 最新日志文件：" . basename($latestLog));
        
        // 读取日志内容，查找导入相关的信息
        $logContent = file_get_contents($latestLog);
        $importLines = [];
        
        $lines = explode("\n", $logContent);
        foreach ($lines as $line) {
            if (strpos($line, 'import') !== false || 
                strpos($line, 'Import') !== false ||
                strpos($line, 'Excel') !== false ||
                strpos($line, 'field mapping') !== false) {
                $importLines[] = $line;
            }
        }
        
        if (!empty($importLines)) {
            $output->writeln("  - 找到导入相关日志：");
            foreach (array_slice($importLines, -10) as $line) {
                $output->writeln("    " . trim($line));
            }
        } else {
            $output->writeln("  - 未找到导入相关日志");
        }
    }
    
    /**
     * 提供解决方案
     */
    private function provideSolutions($output)
    {
        $output->writeln("\n💡 解决方案：");
        
        $output->writeln("1. 检查Excel表头格式：");
        $output->writeln("   - 确保第一行表头完全匹配：学号、身份证号、姓名等");
        $output->writeln("   - 删除表头中的多余空格");
        $output->writeln("   - 确保没有特殊字符");
        
        $output->writeln("\n2. 使用标准模板：");
        $output->writeln("   - 访问 /admin/student/import 下载标准模板");
        $output->writeln("   - 在标准模板基础上填写数据");
        
        $output->writeln("\n3. 检查数据格式：");
        $output->writeln("   - 学号必须是数字");
        $output->writeln("   - 身份证号必须是18位");
        $output->writeln("   - 性别必须是'男'或'女'");
        $output->writeln("   - 班级名称必须在系统中存在");
        
        $output->writeln("\n4. 临时解决方案：");
        $output->writeln("   - 可以修改控制器，增加更宽松的字段匹配");
        $output->writeln("   - 或者提供更详细的错误信息");
    }
}

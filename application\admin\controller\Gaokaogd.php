<?php

namespace app\admin\controller;

use app\common\controller\Backend;

use app\admin\library\Auth;
use Exception;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\db\exception\BindParamException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\response\Json;
/**
 * 高考报名（广东及港澳台户籍）
 *
 * @icon fa fa-circle-o
 */
class Gaokaogd extends Backend
{

    /**
     * Gaokaogd模型对象
     * @var \app\admin\model\Gaokaogd
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Gaokaogd;
        $this->view->assign("registrationTypeList", $this->model->getRegistrationTypeList());
        $this->view->assign("householdTypeList", $this->model->getHouseholdTypeList());
        $this->view->assign("fullThreeYearsList", $this->model->getFullThreeYearsList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true; // 恢复关联查询
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 恢复模型关联查询
            $list = $this->model
                    ->with(['student','certificate'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            // 恢复原来的数据处理方式
            // 批量获取班级信息，避免N+1查询问题
            $studentIds = [];
            foreach ($list as $row) {
                if ($row->getRelation('student') && $row->getRelation('student')->zz_banji_id) {
                    $studentIds[] = $row->getRelation('student')->zz_banji_id;
                }
            }

            // 一次性查询所有需要的班级信息
            $banjiList = [];
            if (!empty($studentIds)) {
                $banjiData = \app\admin\model\Banji::whereIn('id', array_unique($studentIds))->select();
                foreach ($banjiData as $banji) {
                    $banjiList[$banji->id] = $banji->name;
                }
            }

            foreach ($list as $row) {
                $row->visible(['student']);
                $row->getRelation('student')->visible(['student_no','name','gender','phone','father_phone','mother_phone','native_place_city','household_register_city','nation','political_status','address','status']);
                $row->visible(['certificate']);
                $row->getRelation('certificate')->visible(['name','certificate_date']);

                // 设置班级名称
                if ($row->getRelation('student') && $row->getRelation('student')->zz_banji_id) {
                    $row->banji_name = $banjiList[$row->getRelation('student')->zz_banji_id] ?? '';
                } else {
                    $row->banji_name = '';
                }

                // 设置可见字段，包含班级名称
                $row->visible(['id','registration_type','household_type','household_address_city','move_in_date','school_roll_location','full_three_years','school_roll_no','id_card','banji_name']);
            }

            // 恢复原来的返回方式
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


     /**
     * 复写  添加
     */
/**
 * 添加
 *
 * @return string
 * @throws \think\Exception
 */
public function add()
{
    if (false === $this->request->isPost()) {
        return $this->view->fetch();
    }
    $params = $this->request->post('row/a');
    if (empty($params)) {
        $this->error(__('Parameter %s can not be empty', ''));
    }
    $params = $this->preExcludeFields($params);

    $studentNo = $params['student_no'] ?? '';
    if ($studentNo) {
        $studentModel = new \app\admin\model\Student(); // 假设学生信息模型为 Student
        $studentInfo = $studentModel->where('student_no', $studentNo)->find();
        if ($studentInfo) {
            $params['id_card'] = $studentInfo->id_card;
        }
    }

    // 新增逻辑：判断是否为大陆身份证（18 位）且非 44 开头
    $idCard = $params['id_card'] ?? '';
    $moveInDate = $params['move_in_date'] ?? '';
    if (strlen($idCard) == 18 && substr($idCard, 0, 2) !== '44' && !$moveInDate) {
        $this->error('您是非 44 开头的广东户籍学生，请填写迁入时间');
    }
    // 判断非 44 到这结束

    // 处理 move_in_date 为空字符串的情况
    if ($moveInDate === '') {
        $params['move_in_date'] = null;
    }

    // 处理 zz_student_certificate_id 为空或0的情况
    if (empty($params['zz_student_certificate_id']) || $params['zz_student_certificate_id'] == '0') {
        $params['zz_student_certificate_id'] = null;
    }

    if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
        $params[$this->dataLimitField] = $this->auth->id;
    }
    $result = false;
    Db::startTrans();
    try {
        // 是否采用模型验证
        if ($this->modelValidate) {
            $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
            $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
            $this->model->validateFailException()->validate($validate);
        }
        $result = $this->model->allowField(true)->save($params);
        Db::commit();
    } catch (ValidateException|PDOException|Exception $e) {
        Db::rollback();
        $this->error($e->getMessage());
    }
    if ($result === false) {
        $this->error(__('No rows were inserted'));
    }
    $this->success();
}
/**
 * 根据学号获取学生信息
 *
 * @return Json
 */
public function getStudentInfo()
{
    $studentNo = $this->request->post('student_no');
    if (!$studentNo) {
        return json(['code' => 0, 'msg' => '学号不能为空']);
    }

    $studentModel = new \app\admin\model\Student(); // 假设学生信息模型为 Student
    $studentInfo = $studentModel->where('student_no', $studentNo)->find();

    /*$studentInfo = $studentModel//使用模型类查询
    ->alias('s')
    ->join('zz_student_certificate sc', 's.id = sc.student_id', 'LEFT') // 假设关联字段为 student_id，使用 LEFT JOIN 确保即使没有证书信息也能获取学生信息
    ->where('s.student_no', $studentNo)
    ->field('s.id as student_id, s.name, s.id_card, sc.id as certificate_id')
    ->find();*/

    $studentInfo = Db::table('zz_student')//使用数据表查询
    ->alias('s')
    ->join('zz_student_certificate sc', 's.id = sc.zz_student_id', 'LEFT')
    ->join('zz_banji b', 's.zz_banji_id = b.id', 'LEFT')
    ->where('s.student_no', $studentNo)
    ->field('s.id as zz_student_id, s.name, s.id_card, b.name as banji_name, sc.id as zz_student_certificate_id')
    ->find();

    if ($studentInfo) {
        return json(['code' => 1, 'data' => [
            /*'name' => $studentInfo->name,
            'id_card' => $studentInfo->id_card,
            'student_id' => $studentInfo->id, // 假设学生表的主键为 id
            'certificate_id' => $studentInfo->zz_student_certificate_id // 假设学生表中有该字段
*/
            'name' => $studentInfo['name'],
            'id_card' => $studentInfo['id_card'],
            'student_id' => $studentInfo['zz_student_id'],
            'banji_name' => $studentInfo['banji_name'],
            'certificate_id' => $studentInfo['zz_student_certificate_id']

        ]]);
    } else {
        return json(['code' => 0, 'msg' => '未找到该学号对应的学生信息']);
    }
}

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->with(['student', 'certificate'])->find($ids);

        // 单独获取班级信息
        if ($row && $row->student && $row->student->zz_banji_id) {
            $banji = \app\admin\model\Banji::find($row->student->zz_banji_id);
            $row->student->banji_name = $banji ? $banji->name : '';
        }
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 处理 move_in_date 为空字符串的情况
                if (isset($params['move_in_date']) && $params['move_in_date'] === '') {
                    $params['move_in_date'] = null;
                }

                // 处理 zz_student_certificate_id 为空或0的情况
                if (empty($params['zz_student_certificate_id']) || $params['zz_student_certificate_id'] == '0') {
                    $params['zz_student_certificate_id'] = null;
                }

                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 全局同步证书信息
     */
    public function synccertificates()
    {


        try {
            // 获取所有高考报名记录
            $gaokaogdList = $this->model->select();
            $updateCount = 0;

            foreach ($gaokaogdList as $gaokaogd) {
                if ($gaokaogd->zz_student_id) {
                    // 查询该学生最新的证书信息
                    $certificate = \app\admin\model\Stucertifi::where('zz_student_id', $gaokaogd->zz_student_id)
                        ->order('id desc')
                        ->find();

                    $newCertificateId = $certificate ? $certificate->id : null;

                    // 如果证书信息有变化，则更新
                    if ($gaokaogd->zz_student_certificate_id != $newCertificateId) {
                        $gaokaogd->zz_student_certificate_id = $newCertificateId;
                        $result = $gaokaogd->save();
                        if ($result !== false) {
                            $updateCount++;
                        }
                    }
                }
            }

            $this->success("同步完成，共更新了 {$updateCount} 条记录");
        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HTTP响应异常，这是框架正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            \think\Log::write('同步证书异常详情: ' . $e->getMessage() . ' 文件: ' . $e->getFile() . ' 行号: ' . $e->getLine() . ' 堆栈: ' . $e->getTraceAsString(), 'error');
            $this->error('同步失败：' . $e->getMessage());
        }
    }

    /**
     * 单个记录刷新证书信息
     */
    public function refreshcertificate()
    {


        $id = $this->request->post('id');
        if (!$id) {
            $this->error('参数错误');
        }

        try {
            $gaokaogd = $this->model->find($id);
            if (!$gaokaogd) {
                $this->error('记录不存在');
            }

            if ($gaokaogd->zz_student_id) {
                // 查询该学生最新的证书信息
                $certificate = \app\admin\model\Stucertifi::where('zz_student_id', $gaokaogd->zz_student_id)
                    ->order('id desc')
                    ->find();

                $newCertificateId = $certificate ? $certificate->id : null;
                $gaokaogd->zz_student_certificate_id = $newCertificateId;
                $gaokaogd->save();

                $this->success('证书信息已刷新');
            } else {
                $this->error('该记录没有关联的学生信息');
            }
        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HTTP响应异常，这是框架正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            \think\Log::write('刷新证书失败: ' . $e->getMessage() . ' 文件: ' . $e->getFile() . ' 行号: ' . $e->getLine(), 'error');
            $this->error('刷新失败：' . $e->getMessage());
        }
    }

    public function import(){ //导入
        // 如果是GET请求，显示导入页面
        if ($this->request->isGet()) {
            return parent::import();
        }

        // 设置预处理回调来过滤空数据和处理关联
        $this->importCallback = function ($row, $index = 0) {
            $rowNumber = $index + 2; // Excel行号（从第2行开始，第1行是标题）

            // 检查必要字段（支持Excel中的"学号"字段或身份证号）
            $studentNo = trim($row['学号'] ?? $row['student_no'] ?? '');
            $idCard = trim($row['id_card'] ?? '');

            if (empty($studentNo) && empty(trim($row['zz_student_id'] ?? '')) && empty($idCard)) {
                return null; // 返回null表示跳过这一行
            }

            // 验证学号格式
            if (!empty($studentNo) && !is_numeric($studentNo)) {
                throw new \Exception("第{$rowNumber}行：学号格式不正确，应为数字，当前值：{$studentNo}");
            }

            // 验证身份证号格式
            if (!empty($idCard)) {
                $isValid = false;
                $errorDetail = '';

                // 18位身份证验证
                if (strlen($idCard) == 18) {
                    if (!preg_match('/^[1-9]\d{5}/', $idCard)) {
                        $errorDetail = '前6位地区码格式错误';
                    } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}/', $idCard)) {
                        $errorDetail = '出生年份格式错误（应为18xx、19xx或20xx）';
                    } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])/', $idCard)) {
                        $errorDetail = '出生月份格式错误（应为01-12）';
                    } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])/', $idCard)) {
                        $errorDetail = '出生日期格式错误（应为01-31）';
                    } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/', $idCard)) {
                        $errorDetail = '最后4位格式错误';
                    } else {
                        $isValid = true;
                    }
                }
                // 15位身份证验证
                elseif (strlen($idCard) == 15) {
                    if (!preg_match('/^[1-9]\d{7}(0\d|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}$/', $idCard)) {
                        $errorDetail = '15位身份证格式错误';
                    } else {
                        $isValid = true;
                    }
                } else {
                    $errorDetail = '长度错误（应为15位或18位）';
                }

                if (!$isValid) {
                    throw new \Exception("第{$rowNumber}行：身份证号格式不正确，{$errorDetail}，当前值：{$idCard}");
                }
            }

            // 如果有学号，需要转换为学生ID
            if (!empty($studentNo)) {
                $student = \app\admin\model\Student::where('student_no', $studentNo)->find();
                if ($student) {
                    $row['zz_student_id'] = $student->id;

                    // 检查是否已存在该学生的高考报名记录
                    $existing = $this->model->where('zz_student_id', $student->id)->find();
                    if ($existing) {
                        throw new \Exception("第{$rowNumber}行：学号为【{$studentNo}】的学生【{$student->name}】已存在高考报名记录，请勿重复导入");
                    }

                    // 如果有证书名称，查找对应的学生证书
                    $certificateName = trim($row['证书名称'] ?? '');
                    if (!empty($certificateName)) {
                        $certificate = \app\admin\model\Stucertifi::where('zz_student_id', $student->id)
                                                                  ->where('name', $certificateName)
                                                                  ->find();
                        if ($certificate) {
                            $row['zz_student_certificate_id'] = $certificate->id;
                        }
                    }

                    // 处理迁入时间格式
                    if (isset($row['迁入时间']) && is_numeric($row['迁入时间'])) {
                        $excelDate = $row['迁入时间'];
                        if ($excelDate > 0) {
                            $unixTimestamp = ($excelDate - 25569) * 86400;
                            $row['move_in_date'] = date('Y-m-d', $unixTimestamp);
                        }
                        unset($row['迁入时间']);
                    }

                    // 移除Excel中不需要的字段
                    $fieldsToRemove = ['学号', 'student_no', '姓名', '性别(单选)', '联系电话', '父亲电话', '母亲电话', '籍贯', '民族', '政治面貌', '现居地址', '状态', '证书名称', '获证日期'];
                    foreach ($fieldsToRemove as $field) {
                        unset($row[$field]);
                    }
                } else {
                    \think\Log::write('Student not found for student_no: ' . $studentNo, 'debug');
                    return null;
                }
            }
            // 如果没有学号但有身份证号，通过身份证号查找学生
            elseif (!empty($idCard) && empty(trim($row['zz_student_id'] ?? ''))) {
                $student = \app\admin\model\Student::where('id_card', $idCard)->find();
                if ($student) {
                    $row['zz_student_id'] = $student->id;

                    // 检查是否已存在该学生的高考报名记录
                    $existing = $this->model->where('zz_student_id', $student->id)->find();
                    if ($existing) {
                        throw new \Exception("身份证号为【{$idCard}】的学生【{$student->name}】已存在高考报名记录，请勿重复导入");
                    }

                    // 如果有证书名称，查找对应的学生证书
                    $certificateName = trim($row['证书名称'] ?? '');
                    if (!empty($certificateName)) {
                        $certificate = \app\admin\model\Stucertifi::where('zz_student_id', $student->id)
                                                                  ->where('name', $certificateName)
                                                                  ->find();
                        if ($certificate) {
                            $row['zz_student_certificate_id'] = $certificate->id;
                        }
                    }

                    // 处理迁入时间格式
                    if (isset($row['迁入时间']) && is_numeric($row['迁入时间'])) {
                        $excelDate = $row['迁入时间'];
                        if ($excelDate > 0) {
                            $unixTimestamp = ($excelDate - 25569) * 86400;
                            $row['move_in_date'] = date('Y-m-d', $unixTimestamp);
                        }
                        unset($row['迁入时间']);
                    }

                    // 移除Excel中不需要的字段
                    $fieldsToRemove = ['学号', 'student_no', '姓名', '性别(单选)', '联系电话', '父亲电话', '母亲电话', '籍贯', '民族', '政治面貌', '现居地址', '状态', '证书名称', '获证日期'];
                    foreach ($fieldsToRemove as $field) {
                        unset($row[$field]);
                    }

                } else {
                    throw new \Exception("第{$rowNumber}行：未找到身份证号为【{$idCard}】的学生信息");
                }
            }

            return $row;
        };

        try {
            return parent::import();
        } catch (\Exception $e) {
            // 如果是我们自定义的友好错误信息，直接显示
            if (strpos($e->getMessage(), '学号为') !== false) {
                $this->error($e->getMessage());
            }
            // 如果是数据库重复键错误，提供更友好的提示
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $this->error('导入失败：存在重复的高考报名记录，请检查Excel文件中是否有重复的学生信息');
            }
            // 其他错误继续抛出
            throw $e;
        }
    }
}
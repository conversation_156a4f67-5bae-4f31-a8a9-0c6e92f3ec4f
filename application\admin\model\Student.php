<?php

namespace app\admin\model;

use think\Model;
use traits\model\SoftDelete;

class Student extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'student';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'gender_text',
        'status_text'
    ];

    // 获取导入字段配置
    public function getImportFields()
    {
        return [
            'student_no'        => '学号',
            'id_card'           => '身份证号',
            'name'              => '姓名',
            'gender'            => '性别(单选)',
            'phone'             => '联系电话',
            'father_phone'      => '父亲电话',
            'mother_phone'      => '母亲电话',
            'native_place_city' => '籍贯',
            'household_register_city'=> '户籍所在地',
            'nation'            => '民族',
            'political_status'  => '政治面貌',
            'address'           => '现居地址',
            'status'            => '状态',
            'zz_banji_id'       => '班级名称',
            'zz_dorm_id'        => '宿舍号',
        ];
    }



    public function getGenderList()
    {
        return ['male' => __('Gender male'), 'female' => __('Gender female')];
    }

    public function getStatusList()
    {
        return ['studying' => __('Studying'), 'graduated' => __('Graduated')];
    }

    public function getGenderTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['gender']) ? $data['gender'] : '');
        $list = $this->getGenderList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function banji()
    {
        return $this->belongsTo('app\admin\model\Banji', 'zz_banji_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function dorm()
    {
        return $this->belongsTo('app\admin\model\Dorm', 'zz_dorm_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

   /* public function generateImportTemplate()
    {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 设置表头
        $headers = array_values($this->getImportFields());
        $sheet->fromArray($headers, null, 'A1');

        // 添加数据验证（班级下拉）
        $banjiNames = \app\admin\model\Banji::column('name');
        $validation = $sheet->getDataValidation('M2:M1048576');
        $validation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
        $validation->setFormula1('"' . implode(',', $banjiNames) . '"');

        // 添加数据验证（宿舍下拉）
        $dormNames = \app\admin\model\Dorm::column('name');
        $validation = $sheet->getDataValidation('N2:N1048576');
        $validation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
        $validation->setFormula1('"' . implode(',', $dormNames) . '"');

        // 输出文件
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="学生导入模板.xlsx"');
        $writer->save('php://output');
        exit;
    }*/
}
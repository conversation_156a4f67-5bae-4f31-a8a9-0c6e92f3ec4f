define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'student/index' + location.search,
                    add_url: 'student/add',
                    edit_url: 'student/edit',
                    del_url: 'student/del',
                    multi_url: 'student/multi',
                    import_url: 'student/import',
                    table: 'student',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'student_no', title: __('Student_no'), operate: 'LIKE'},
                        {field: 'id_card', title: __('Id_card')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'gender', title: __('Gender'), searchList: {"male":__('Gender male'),"female":__('Gender female')}, formatter: Table.api.formatter.normal},
                        {field: 'phone', title: __('Phone'), operate: 'LIKE'},
                        {field: 'father_phone', title: __('Father_phone'), operate: 'LIKE'},
                        {field: 'mother_phone', title: __('Mother_phone'), operate: 'LIKE'},
                        {field: 'native_place_city', title: __('Native_place_city'), operate: 'LIKE'},
                        {field: 'household_register_city', title: __('household_register_city'), operate: 'LIKE'},
                        {field: 'nation', title: __('Nation'), operate: 'LIKE'},
                        {field: 'political_status', title: __('Political_status'), operate: 'LIKE', formatter: Table.api.formatter.status},
                        {field: 'address', title: __('Address'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"studying":__('Studying'),"graduated":__('Graduated')}, formatter: Table.api.formatter.status},
                        {field: 'banji.name', title: __('Banji.name'), operate: 'LIKE'},
                        {field: 'dorm.name', title: __('Dorm.name'), operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'student/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), align: 'left'},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '140px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'student/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'student/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

<?php

namespace app\admin\model;

use think\Model;
use traits\model\SoftDelete;

class Teacher extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'teacher';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'gender_text'
    ];
    

    
    public function getGenderList()
    {
        return ['male' => __('Gender male'), 'female' => __('Gender female')];
    }


    public function getGenderTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['gender']) ? $data['gender'] : '');
        $list = $this->getGenderList();
        return isset($list[$value]) ? $list[$value] : '';
    }




}

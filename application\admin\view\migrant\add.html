<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_student_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zz_student_id" data-rule="required" min="0" data-source="student/index" class="form-control selectpage" name="row[zz_student_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_student_certificate_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zz_student_certificate_id" min="0" data-rule="required" data-source="student/certificate/index" class="form-control selectpage" name="row[zz_student_certificate_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Application_category')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-application_category" class="form-control selectpicker" name="row[application_category]">
                {foreach name="applicationCategoryList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Class_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-class_name" class="form-control" name="row[class_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Student_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-student_number" class="form-control" name="row[student_number]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Student_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-student_name" data-rule="required" class="form-control" name="row[student_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Student_id_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-student_id_card" data-rule="required" class="form-control" name="row[student_id_card]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Student_household_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-student_household_type" data-rule="required" class="form-control" name="row[student_household_type]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_same_household_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_same_household_type" class="form-control selectpicker" name="row[is_same_household_type]">
                {foreach name="isSameHouseholdTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Social_security_contributor')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-social_security_contributor" class="form-control" name="row[social_security_contributor]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ss_contributor_household')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ss_contributor_household" class="form-control" name="row[ss_contributor_household]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ss_contributor_id_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ss_contributor_id_card" class="form-control" name="row[ss_contributor_id_card]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ss_relationship_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-ss_relationship_type" class="form-control selectpicker" name="row[ss_relationship_type]">
                {foreach name="ssRelationshipTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Social_security_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-social_security_city" class="form-control" data-toggle="city-picker" name="row[social_security_city]" type="text"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pension_insurance_months')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pension_insurance_months" class="form-control" name="row[pension_insurance_months]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Medical_insurance_months')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-medical_insurance_months" class="form-control" name="row[medical_insurance_months]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ss_is_same_household_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-ss_is_same_household_type" class="form-control selectpicker" name="row[ss_is_same_household_type]">
                {foreach name="ssIsSameHouseholdTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Residence_permit_contributor')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-residence_permit_contributor" class="form-control" name="row[residence_permit_contributor]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rp_contributor_household')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rp_contributor_household" class="form-control" name="row[rp_contributor_household]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rp_contributor_id_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rp_contributor_id_card" class="form-control" name="row[rp_contributor_id_card]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rp_relationship_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-rp_relationship_type" class="form-control selectpicker" name="row[rp_relationship_type]">
                {foreach name="rpRelationshipTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Residence_permit_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-residence_permit_city" class="form-control" data-toggle="city-picker" name="row[residence_permit_city]" type="text"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Residence_permit_start_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-residence_permit_start_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[residence_permit_start_date]" type="text" value="{:date('Y-m-d')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_residence_permit_valid')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_residence_permit_valid" class="form-control selectpicker" name="row[is_residence_permit_valid]">
                {foreach name="isResidencePermitValidList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Has_three_year_school_roll')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-has_three_year_school_roll" data-rule="required" class="form-control selectpicker" name="row[has_three_year_school_roll]">
                {foreach name="hasThreeYearSchoolRollList" item="vo"}
                    <option value="{$key}" {in name="key" value="是"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('School_roll_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-school_roll_no" class="form-control" name="row[school_roll_no]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('School_review_status_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-school_review_status_type" class="form-control selectpicker" name="row[school_review_status_type]">
                {foreach name="schoolReviewStatusTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="未审查"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Review_opinion')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-review_opinion" class="form-control " rows="5" name="row[review_opinion]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Has_certificate')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-has_certificate" class="form-control selectpicker" name="row[has_certificate]">
                {foreach name="hasCertificateList" item="vo"}
                    <option value="{$key}" {in name="key" value="否"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

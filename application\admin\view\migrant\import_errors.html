{extend name="admin@layout/default" /}

{block name="content"}
<style>
    .error-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 20px;
    }
    .error-header {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        padding: 25px 30px;
        text-align: center;
    }
    .error-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 300;
    }
    .error-header .subtitle {
        margin-top: 10px;
        opacity: 0.9;
        font-size: 16px;
    }
    .error-stats {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px 30px;
        margin: 0;
        display: flex;
        justify-content: center;
        gap: 40px;
    }
    .stats-item {
        text-align: center;
    }
    .stats-number {
        font-size: 24px;
        font-weight: bold;
        color: #856404;
        display: block;
    }
    .stats-label {
        font-size: 14px;
        color: #856404;
        margin-top: 5px;
    }
    .error-content {
        padding: 0;
        max-height: 60vh;
        overflow-y: auto;
    }
    .error-row {
        border-bottom: 1px solid #eee;
        padding: 25px 30px;
        transition: background-color 0.2s;
    }
    .error-row:hover {
        background-color: #f8f9fa;
    }
    .error-row:last-child {
        border-bottom: none;
    }
    .row-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    .row-number {
        background: #dc3545;
        color: white;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: bold;
        margin-right: 15px;
    }
    .error-count {
        background: #6c757d;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
        margin-left: 10px;
    }
    .error-list {
        margin: 0;
        padding: 0;
    }
    .error-item {
        margin-bottom: 12px;
        padding: 12px 16px;
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 6px;
        color: #721c24;
        font-size: 14px;
        line-height: 1.5;
        display: flex;
        align-items: flex-start;
    }
    .error-item:last-child {
        margin-bottom: 0;
    }
    .error-item i {
        margin-right: 10px;
        color: #dc3545;
        margin-top: 2px;
        flex-shrink: 0;
    }
    .action-buttons {
        padding: 25px 30px;
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        text-align: center;
    }
    .btn-custom {
        padding: 12px 30px;
        margin: 0 10px;
        border-radius: 25px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s;
        border: none;
        cursor: pointer;
    }
    .btn-primary-custom {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
    }
    .btn-primary-custom:hover {
        background: linear-gradient(135deg, #0056b3, #004085);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    }
    .btn-secondary-custom {
        background: #6c757d;
        color: white;
    }
    .btn-secondary-custom:hover {
        background: #545b62;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(108,117,125,0.3);
    }
    .tips {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 6px;
        padding: 20px;
        margin: 20px 30px;
        color: #0c5460;
    }
    .tips h5 {
        margin: 0 0 15px 0;
        color: #0c5460;
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
    }
    .tips h5 i {
        margin-right: 8px;
        color: #17a2b8;
    }
    .tips ul {
        margin: 0;
        padding-left: 20px;
    }
    .tips li {
        margin-bottom: 8px;
        font-size: 13px;
        line-height: 1.4;
    }
</style>

<div class="error-container">
    <!-- 错误标题 -->
    <div class="error-header">
        <h1><i class="fa fa-exclamation-triangle"></i> 数据导入失败</h1>
        <div class="subtitle">发现数据格式错误，请修正后重新导入</div>
    </div>

    <!-- 错误统计 -->
    <div class="error-stats">
        <div class="stats-item">
            <span class="stats-number">{$total_rows}</span>
            <span class="stats-label">行有错误</span>
        </div>
        <div class="stats-item">
            <span class="stats-number">{$total_errors}</span>
            <span class="stats-label">个错误</span>
        </div>
    </div>

    <!-- 错误详情 -->
    <div class="error-content">
        {foreach name="errors" item="rowErrors" key="rowNumber"}
        <div class="error-row">
            <div class="row-header">
                <span class="row-number">第 {$rowNumber} 行</span>
                <span class="error-count">{$rowErrors|count} 个错误</span>
            </div>
            <div class="error-list">
                {foreach name="rowErrors" item="error"}
                <div class="error-item">
                    <i class="fa fa-times-circle"></i>
                    <span>{$error}</span>
                </div>
                {/foreach}
            </div>
        </div>
        {/foreach}
    </div>

    <!-- 修正提示 -->
    <div class="tips">
        <h5><i class="fa fa-lightbulb-o"></i> 修正建议</h5>
        <ul>
            <li><strong>身份证号格式：</strong>18位或15位，月份应为01-12，日期应为01-31</li>
            <li><strong>学号：</strong>必须为纯数字</li>
            <li><strong>报考类别：</strong>只能是：3+证书、三二分段、五年一贯、其他</li>
            <li><strong>是否类型字段：</strong>只能填写：是 或 否</li>
            <li><strong>参保月数：</strong>应为0-600之间的数字</li>
            <li><strong>居住证初办时间：</strong>不能是未来日期</li>
        </ul>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
        <button onclick="history.back();" class="btn-custom btn-secondary-custom">
            <i class="fa fa-arrow-left"></i> 返回重新导入
        </button>
        <a href="{:url('migrant/index')}" class="btn-custom btn-primary-custom">
            <i class="fa fa-list"></i> 返回列表
        </a>
    </div>
</div>
{/block}

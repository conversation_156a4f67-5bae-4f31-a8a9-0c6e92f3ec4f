<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 修复高考报名数据关联
 */
class FixGaokaogdRelations extends Command
{
    protected function configure()
    {
        $this->setName('fix:gaokaogd-relations')
            ->setDescription('修复高考报名数据与学生、证书的关联关系');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始修复高考报名数据关联...');
        
        $fixed = 0;
        
        // 1. 通过身份证号关联学生
        $fixed += $this->linkStudentsByIdCard($output);
        
        // 2. 关联证书信息
        $fixed += $this->linkCertificates($output);
        
        // 3. 清理无效关联
        $fixed += $this->cleanInvalidRelations($output);
        
        $output->writeln("<info>✅ 关联修复完成，共修复 {$fixed} 条记录！</info>");
        
        return 0;
    }
    
    /**
     * 通过身份证号关联学生
     */
    private function linkStudentsByIdCard($output)
    {
        $output->writeln('通过身份证号关联学生信息...');
        
        $fixed = 0;
        
        // 查找可以通过身份证号关联的记录
        $linkableRecords = Db::query("
            SELECT g.id, g.id_card, s.id as student_id, s.name, s.student_no
            FROM zz_gaokao_apply_local g
            INNER JOIN zz_student s ON g.id_card = s.id_card
            WHERE g.zz_student_id IS NULL
        ");
        
        foreach ($linkableRecords as $record) {
            try {
                Db::table('zz_gaokao_apply_local')
                    ->where('id', $record['id'])
                    ->update(['zz_student_id' => $record['student_id']]);
                
                $output->writeln("  ✅ 关联成功：身份证 {$record['id_card']} -> 学生 {$record['name']}({$record['student_no']})");
                $fixed++;
                
            } catch (\Exception $e) {
                $output->writeln("  ❌ 关联失败：身份证 {$record['id_card']} - " . $e->getMessage());
            }
        }
        
        if (count($linkableRecords) == 0) {
            $output->writeln('  - 没有找到可通过身份证号关联的记录');
        }
        
        return $fixed;
    }
    
    /**
     * 关联证书信息
     */
    private function linkCertificates($output)
    {
        $output->writeln('关联证书信息...');
        
        $fixed = 0;
        
        // 查找有学生ID但没有证书ID的记录，且该学生有证书
        $linkableCertificates = Db::query("
            SELECT g.id, g.zz_student_id, c.id as certificate_id, c.name as cert_name, s.name as student_name
            FROM zz_gaokao_apply_local g
            INNER JOIN zz_student s ON g.zz_student_id = s.id
            INNER JOIN zz_student_certificate c ON s.id = c.zz_student_id
            WHERE g.zz_student_certificate_id IS NULL
        ");
        
        foreach ($linkableCertificates as $record) {
            try {
                Db::table('zz_gaokao_apply_local')
                    ->where('id', $record['id'])
                    ->update(['zz_student_certificate_id' => $record['certificate_id']]);
                
                $output->writeln("  ✅ 证书关联成功：学生 {$record['student_name']} -> 证书 {$record['cert_name']}");
                $fixed++;
                
            } catch (\Exception $e) {
                $output->writeln("  ❌ 证书关联失败：学生 {$record['student_name']} - " . $e->getMessage());
            }
        }
        
        if (count($linkableCertificates) == 0) {
            $output->writeln('  - 没有找到可关联的证书记录');
        }
        
        return $fixed;
    }
    
    /**
     * 清理无效关联
     */
    private function cleanInvalidRelations($output)
    {
        $output->writeln('清理无效关联...');
        
        $fixed = 0;
        
        // 清理无效的学生ID
        $invalidStudentIds = Db::query("
            SELECT g.id FROM zz_gaokao_apply_local g
            LEFT JOIN zz_student s ON g.zz_student_id = s.id
            WHERE g.zz_student_id IS NOT NULL AND s.id IS NULL
        ");
        
        foreach ($invalidStudentIds as $record) {
            Db::table('zz_gaokao_apply_local')
                ->where('id', $record['id'])
                ->update(['zz_student_id' => null]);
            $fixed++;
        }
        
        if (count($invalidStudentIds) > 0) {
            $output->writeln("  - 清理无效学生ID：" . count($invalidStudentIds) . " 条");
        }
        
        // 清理无效的证书ID
        $invalidCertificateIds = Db::query("
            SELECT g.id FROM zz_gaokao_apply_local g
            LEFT JOIN zz_student_certificate c ON g.zz_student_certificate_id = c.id
            WHERE g.zz_student_certificate_id IS NOT NULL AND c.id IS NULL
        ");
        
        foreach ($invalidCertificateIds as $record) {
            Db::table('zz_gaokao_apply_local')
                ->where('id', $record['id'])
                ->update(['zz_student_certificate_id' => null]);
            $fixed++;
        }
        
        if (count($invalidCertificateIds) > 0) {
            $output->writeln("  - 清理无效证书ID：" . count($invalidCertificateIds) . " 条");
        }
        
        return $fixed;
    }
}

<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <!-- 隐藏的主记录ID字段 -->
    <input name="row[id]" type="hidden" value="{$row.id|htmlentities}">

    <!-- 学生信息显示（只读） -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('学生信息')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="form-control-static" style="padding: 8px; background-color: #f5f5f5; border-radius: 4px;">
                <strong>学号：</strong>{$row.student.student_no|default='-'} &nbsp;&nbsp;
                <strong>姓名：</strong>{$row.student.name|default='-'} &nbsp;&nbsp;
                <strong>班级：</strong>{$row.student.banji_name|default='未分配班级'} &nbsp;&nbsp;
                <strong>身份证：</strong>{$row.student.id_card|default='-'}
            </div>
            <!-- 隐藏的学生ID字段 -->
            <input id="c-zz_student_id" name="row[zz_student_id]" type="hidden" value="{$row.zz_student_id|htmlentities}">
        </div>
    </div>

    <!-- 证书信息显示（只读） -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('证书信息')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="form-control-static" style="padding: 8px; background-color: #f5f5f5; border-radius: 4px;">
                {if condition="$row.certificate"}
                    <strong>证书名称：</strong>{$row.certificate.name|default='-'} &nbsp;&nbsp;
                    <strong>颁发机构：</strong>{$row.certificate.issuing_authority|default='-'} &nbsp;&nbsp;
                    <strong>颁发日期：</strong>{$row.certificate.certificate_date|default='-'}
                {else /}
                    <span style="color: #999;">暂无证书信息</span>
                {/if}
            </div>
            <button type="button" id="refresh-certificate-edit" class="btn btn-sm btn-info" style="margin-top: 5px;">
                <i class="fa fa-refresh"></i> 刷新证书信息
            </button>
            <!-- 隐藏的证书ID字段 -->
            <input id="c-zz_student_certificate_id" name="row[zz_student_certificate_id]" type="hidden" value="{$row.zz_student_certificate_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Registration_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-registration_type" data-rule="required" class="form-control selectpicker" name="row[registration_type]">
                {foreach name="registrationTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.registration_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Household_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-household_type" data-rule="required" class="form-control selectpicker" name="row[household_type]">
                {foreach name="householdTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.household_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Household_address_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-household_address_city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[household_address_city]" type="text" value="{$row.household_address_city|htmlentities}"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Move_in_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-move_in_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[move_in_date]" type="text" value="{$row.move_in_date}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('School_roll_location')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-school_roll_location" data-rule="required" class="form-control" name="row[school_roll_location]" type="text" value="{$row.school_roll_location|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Full_three_years')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-full_three_years" data-rule="required" class="form-control selectpicker" name="row[full_three_years]">
                {foreach name="fullThreeYearsList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.full_three_years"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('School_roll_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-school_roll_no" class="form-control" name="row[school_roll_no]" type="text" value="{$row.school_roll_no|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Id_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-id_card" class="form-control" name="row[id_card]" type="text" value="{$row.id_card|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<?php

namespace app\admin\model;

use think\Model;
use traits\model\SoftDelete;

class Migrant extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'migrant_student_application';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'application_category_text',
        'is_same_household_type_text',
        'ss_relationship_type_text',
        'ss_is_same_household_type_text',
        'rp_relationship_type_text',
        'is_residence_permit_valid_text',
        'has_three_year_school_roll_text',
        'school_review_status_type_text',
        'has_certificate_text'
    ];

    // 只设置真正需要默认值的字段，避免覆盖导入数据
    // 移除了大部分字段的默认值设置，让导入的数据能正确保存
    protected $insert = [
        'school_review_status_type' => '未审查',
        'has_certificate' => '否'
    ];
    

    
    public function getApplicationCategoryList()
    {
        return ['3+证书' => __('3+证书'), '三二分段' => __('三二分段'), '五年一贯' => __('五年一贯'), '其他' => __('其他')];
    }

    public function getIsSameHouseholdTypeList()
    {
        return ['是' => __('是'), '否' => __('否')];
    }

    public function getSsRelationshipTypeList()
    {
        return ['父亲' => __('父亲'), '母亲' => __('母亲'), '其他法定监护人' => __('其他法定监护人')];
    }

    public function getSsIsSameHouseholdTypeList()
    {
        return ['是' => __('是'), '否' => __('否')];
    }

    public function getRpRelationshipTypeList()
    {
        return ['父亲' => __('父亲'), '母亲' => __('母亲'), '其他法定监护人' => __('其他法定监护人')];
    }

    public function getIsResidencePermitValidList()
    {
        return ['是' => __('是'), '否' => __('否')];
    }

    public function getHasThreeYearSchoolRollList()
    {
        return ['是' => __('是'), '否' => __('否')];
    }

    public function getSchoolReviewStatusTypeList()
    {
        return ['未审查' => __('未审查'), '通过' => __('通过'), '未通过' => __('未通过')];
    }

    public function getHasCertificateList()
    {
        return ['是' => __('是'), '否' => __('否')];
    }


    public function getApplicationCategoryTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['application_category']) ? $data['application_category'] : '');
        $list = $this->getApplicationCategoryList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getIsSameHouseholdTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_same_household_type']) ? $data['is_same_household_type'] : '');
        $list = $this->getIsSameHouseholdTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getSsRelationshipTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['ss_relationship_type']) ? $data['ss_relationship_type'] : '');
        $list = $this->getSsRelationshipTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getSsIsSameHouseholdTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['ss_is_same_household_type']) ? $data['ss_is_same_household_type'] : '');
        $list = $this->getSsIsSameHouseholdTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getRpRelationshipTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['rp_relationship_type']) ? $data['rp_relationship_type'] : '');
        $list = $this->getRpRelationshipTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getIsResidencePermitValidTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_residence_permit_valid']) ? $data['is_residence_permit_valid'] : '');
        $list = $this->getIsResidencePermitValidList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getHasThreeYearSchoolRollTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['has_three_year_school_roll']) ? $data['has_three_year_school_roll'] : '');
        $list = $this->getHasThreeYearSchoolRollList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getSchoolReviewStatusTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['school_review_status_type']) ? $data['school_review_status_type'] : '');
        $list = $this->getSchoolReviewStatusTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getHasCertificateTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['has_certificate']) ? $data['has_certificate'] : '');
        $list = $this->getHasCertificateList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function student()
    {
        return $this->belongsTo('Student', 'zz_student_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function certificate()
    {
        return $this->belongsTo('Stucertifi', 'zz_student_certificate_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    // 注释掉这些获取器，因为它们会干扰with()预加载
    // 如果需要处理ID为0的情况，应该在控制器中处理
    /*
    public function getStudentAttr($value, $data)
    {
        if (isset($data['zz_student_id']) && $data['zz_student_id'] == 0) {
            return null;
        }
        return $this->student();
    }

    public function getCertificateAttr($value, $data)
    {
        if (isset($data['zz_student_certificate_id']) && $data['zz_student_certificate_id'] == 0) {
            return null;
        }
        return $this->certificate();
    }
    */
}

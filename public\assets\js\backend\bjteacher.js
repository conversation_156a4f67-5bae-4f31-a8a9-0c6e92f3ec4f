define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'bjteacher/index' + location.search,
                    add_url: 'bjteacher/add',
                    edit_url: 'bjteacher/edit',
                    del_url: 'bjteacher/del',
                    multi_url: 'bjteacher/multi',
                    import_url: 'bjteacher/import',
                    table: 'bjteacher',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'banji.name', title: __('Banji.name'), operate: 'LIKE'},
                        {field: 'banji.location', title: __('Banji.location'), operate: 'LIKE'},
                        {field: 'teacher.name', title: __('Teacher.name'), operate: 'LIKE'},
                        {field: 'teacher.phone', title: __('Teacher.phone'), operate: 'LIKE'},
                        {field: 'is_current', title: '是否当前班主任', searchList: {"1":"是","0":"否"}, formatter: function(value, row, index) {
                            return value == 1 ? '<span class="text-success">是</span>' : '<span class="text-muted">否</span>';
                        }},
                        {field: 'status', title: '状态', searchList: {"active":"在职","inactive":"离职"}, formatter: function(value, row, index) {
                            return value == 'active' ? '<span class="text-success">在职</span>' : '<span class="text-danger">离职</span>';
                        }},
                        {field: 'start_date', title: __('Start_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'end_date', title: __('End_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'remark', title: '备注', operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 教师信息管理
 *
 * @icon fa fa-circle-o
 */
class Teacher extends Backend
{

    /**
     * Teacher模型对象
     * @var \app\admin\model\Teacher
     */
    protected $model = null;



    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Teacher;
        $this->view->assign("genderList", $this->model->getGenderList());
    }



    /**
     * 导入
     */
    public function import()
    {
        $this->importCallback = function ($row) {
            // 检查是否为空行
            if (empty(trim($row['job_number'] ?? '')) && empty(trim($row['name'] ?? ''))) {
                return null; // 跳过空行
            }

            // 验证必填字段
            if (empty($row['job_number'])) {
                throw new \Exception("工号不能为空");
            }
            if (empty($row['name'])) {
                throw new \Exception("姓名不能为空");
            }

            // 验证工号唯一性
            $exists = $this->model->where('job_number', $row['job_number'])->find();
            if ($exists) {
                throw new \Exception("工号 {$row['job_number']} 已存在");
            }

            // 性别字段转换
            if (isset($row['gender'])) {
                if (in_array($row['gender'], ['男', 'male'])) {
                    $row['gender'] = 'male';
                } elseif (in_array($row['gender'], ['女', 'female'])) {
                    $row['gender'] = 'female';
                }
            }

            // 手动设置时间戳（确保导入时有创建时间）
            $row['createtime'] = time();
            $row['updatetime'] = time();

            return $row;
        };

        return parent::import();
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}

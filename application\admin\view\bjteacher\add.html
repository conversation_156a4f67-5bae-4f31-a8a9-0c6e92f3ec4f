<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_banji_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zz_banji_id" data-rule="required" min="0" data-source="banji/index" class="form-control selectpage" name="row[zz_banji_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_teacher_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zz_teacher_id" data-rule="required" min="0" data-source="teacher/index" class="form-control selectpage" name="row[zz_teacher_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Start_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-start_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[start_date]" type="text" value="{:date('Y-m-d')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('End_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-end_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[end_date]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_current')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-is_current" class="form-control selectpicker" name="row[is_current]">
                <option value="1" selected>是</option>
                <option value="0">否</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-status" class="form-control selectpicker" name="row[status]">
                <option value="active" selected>在职</option>
                <option value="inactive">离职</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" rows="3" name="row[remark]" placeholder="请填写备注信息（如调动原因等）"></textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

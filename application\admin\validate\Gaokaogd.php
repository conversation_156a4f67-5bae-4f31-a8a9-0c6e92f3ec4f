<?php

namespace app\admin\validate;

use think\Validate;

class Gaokaogd extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        //豆包推荐新增规则
        'zz_student_id' => 'require',
        'zz_student_certificate_id' => 'number|nullable', // 允许为空
        'registration_type' => 'require',
        'household_type' => 'require',
        'household_address_city' => 'require',
        'school_roll_location' => 'require',
        'full_three_years' => 'require',
        'move_in_date' => 'date|nullable', // 允许为空
    ];
    /**
     * 提示消息
     */
    protected $message = [
        //豆包推荐新增提示
        'zz_student_id.require' => '学生 ID 不能为空',
        'registration_type.require' => '报名类型不能为空',
        'household_type.require' => '户口性质不能为空',
        'household_address_city.require' => '户口所在地不能为空',
        'school_roll_location.require' => '学籍所在地不能为空',
        'full_three_years.require' => '完整三年学籍信息不能为空',
    ];
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => [],
        'edit' => [],
    ];
    
}

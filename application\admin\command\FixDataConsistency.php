<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 数据一致性修复命令
 * 用于修复外键关联的数据一致性问题
 */
class FixDataConsistency extends Command
{
    protected function configure()
    {
        $this->setName('fix:data-consistency')
            ->setDescription('修复数据一致性问题，特别是外键关联问题');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始修复数据一致性问题...');
        
        $fixed = 0;
        
        // 1. 修复外键字段的0值问题
        $fixed += $this->fixForeignKeyZeroValues($output);
        
        // 2. 修复数据同步问题
        $fixed += $this->fixDataSyncIssues($output);
        
        // 3. 清理无效的外键引用
        $fixed += $this->cleanInvalidForeignKeys($output);
        
        $output->writeln("<info>✅ 数据一致性修复完成，共修复 {$fixed} 个问题！</info>");
        
        return 0;
    }
    
    /**
     * 修复外键字段的0值问题
     * 按技术要点：外键字段用null而不是0来避免唯一性冲突
     */
    private function fixForeignKeyZeroValues($output)
    {
        $fixed = 0;
        $output->writeln('修复外键字段的0值问题...');
        
        // 修复学生表
        $count = Db::table('zz_student')
            ->where('zz_banji_id', 0)
            ->update(['zz_banji_id' => null]);
        if ($count > 0) {
            $output->writeln("  - 学生表班级ID：修复 {$count} 条记录");
            $fixed += $count;
        }
        
        $count = Db::table('zz_student')
            ->where('zz_dorm_id', 0)
            ->update(['zz_dorm_id' => null]);
        if ($count > 0) {
            $output->writeln("  - 学生表宿舍ID：修复 {$count} 条记录");
            $fixed += $count;
        }
        
        // 修复随迁子女报名表
        $count = Db::table('zz_migrant_student_application')
            ->where('zz_student_id', 0)
            ->update(['zz_student_id' => null]);
        if ($count > 0) {
            $output->writeln("  - 随迁子女报名表学生ID：修复 {$count} 条记录");
            $fixed += $count;
        }
        
        $count = Db::table('zz_migrant_student_application')
            ->where('zz_student_certificate_id', 0)
            ->update(['zz_student_certificate_id' => null]);
        if ($count > 0) {
            $output->writeln("  - 随迁子女报名表证书ID：修复 {$count} 条记录");
            $fixed += $count;
        }
        
        // 修复高考报名表
        $count = Db::table('zz_gaokao_apply_local')
            ->where('zz_student_id', 0)
            ->update(['zz_student_id' => null]);
        if ($count > 0) {
            $output->writeln("  - 高考报名表学生ID：修复 {$count} 条记录");
            $fixed += $count;
        }

        $count = Db::table('zz_gaokao_apply_local')
            ->where('zz_student_certificate_id', 0)
            ->update(['zz_student_certificate_id' => null]);
        if ($count > 0) {
            $output->writeln("  - 高考报名表证书ID：修复 {$count} 条记录");
            $fixed += $count;
        }
        
        // 修复证书表
        $count = Db::table('zz_student_certificate')
            ->where('zz_student_id', 0)
            ->update(['zz_student_id' => null]);
        if ($count > 0) {
            $output->writeln("  - 证书表学生ID：修复 {$count} 条记录");
            $fixed += $count;
        }
        
        return $fixed;
    }
    
    /**
     * 修复数据同步问题
     */
    private function fixDataSyncIssues($output)
    {
        $fixed = 0;
        $output->writeln('修复数据同步问题...');
        
        // 同步随迁子女报名表中的学生信息
        $syncData = Db::query("
            SELECT m.id, s.name, s.id_card, s.student_no
            FROM zz_migrant_student_application m
            INNER JOIN zz_student s ON m.zz_student_id = s.id
            WHERE m.student_name != s.name OR m.student_id_card != s.id_card OR m.student_number != s.student_no
        ");
        
        foreach ($syncData as $data) {
            Db::table('zz_migrant_student_application')
                ->where('id', $data['id'])
                ->update([
                    'student_name' => $data['name'],
                    'student_id_card' => $data['id_card'],
                    'student_number' => $data['student_no']
                ]);
            $fixed++;
        }
        
        if (count($syncData) > 0) {
            $output->writeln("  - 同步随迁子女报名表学生信息：修复 " . count($syncData) . " 条记录");
        }
        
        return $fixed;
    }
    
    /**
     * 清理无效的外键引用
     */
    private function cleanInvalidForeignKeys($output)
    {
        $fixed = 0;
        $output->writeln('清理无效的外键引用...');
        
        // 清理随迁子女报名表中无效的学生ID
        $invalidStudentIds = Db::query("
            SELECT m.id FROM zz_migrant_student_application m
            LEFT JOIN zz_student s ON m.zz_student_id = s.id
            WHERE m.zz_student_id IS NOT NULL AND s.id IS NULL
        ");
        
        foreach ($invalidStudentIds as $record) {
            Db::table('zz_migrant_student_application')
                ->where('id', $record['id'])
                ->update(['zz_student_id' => null]);
            $fixed++;
        }
        
        if (count($invalidStudentIds) > 0) {
            $output->writeln("  - 清理随迁子女报名表无效学生ID：修复 " . count($invalidStudentIds) . " 条记录");
        }
        
        // 清理随迁子女报名表中无效的证书ID
        $invalidCertificateIds = Db::query("
            SELECT m.id FROM zz_migrant_student_application m
            LEFT JOIN zz_student_certificate c ON m.zz_student_certificate_id = c.id
            WHERE m.zz_student_certificate_id IS NOT NULL AND c.id IS NULL
        ");
        
        foreach ($invalidCertificateIds as $record) {
            Db::table('zz_migrant_student_application')
                ->where('id', $record['id'])
                ->update(['zz_student_certificate_id' => null]);
            $fixed++;
        }
        
        if (count($invalidCertificateIds) > 0) {
            $output->writeln("  - 清理随迁子女报名表无效证书ID：修复 " . count($invalidCertificateIds) . " 条记录");
        }
        
        return $fixed;
    }
}

<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 宿舍信息管理
 *
 * @icon fa fa-circle-o
 */
class Dorm extends Backend
{

    /**
     * Dorm模型对象
     * @var \app\admin\model\Dorm
     */
    protected $model = null;

    /**
     * 快速搜索时执行查找的字段
     */
    protected $searchFields = 'name';

    /**
     * 是否关联查询
     */
    protected $relationSearch = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Dorm;
    }

    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','bed_count','createtime','updatetime']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    public function import(){ //导入
        // 设置预处理回调来过滤空数据
        $this->importCallback = function ($row) {
            // 检查宿舍名称是否为空（更严格的检查）
            $name = trim($row['name'] ?? '');
            if (empty($name) || $name === '' || $name === null) {
                return null; // 返回null表示跳过这一行
            }

            // 确保床位数是数字
            if (!isset($row['bed_count']) || !is_numeric($row['bed_count']) || $row['bed_count'] === '') {
                $row['bed_count'] = 0;
            } else {
                $row['bed_count'] = (int)$row['bed_count'];
            }

            // 确保名称不为空
            $row['name'] = $name;

            return $row;
        };

        try {
            return parent::import();
        } catch (\Exception $e) {
            // 如果是数据库重复键错误，提供更友好的提示
            if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'name') !== false) {
                // 尝试从错误信息中提取宿舍名称
                if (preg_match("/Duplicate entry '(.+)' for key.*name/", $e->getMessage(), $matches)) {
                    $dormName = $matches[1];
                    $this->error("导入失败，宿舍名称【{$dormName}】已存在，请检查是否重复导入");
                } else {
                    $this->error('导入失败：存在重复的宿舍名称，请检查Excel文件');
                }
            }
            // 其他错误继续抛出
            throw $e;
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}

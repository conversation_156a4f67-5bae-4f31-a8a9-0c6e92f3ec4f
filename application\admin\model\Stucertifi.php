<?php

namespace app\admin\model;

use think\Model;


class Stucertifi extends Model
{

    

    

    // 表名
    protected $name = 'student_certificate';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];

    // 获取导入字段配置
    public function getImportFields()
    {
        return [
            'student_no'         => '学号',
            'name'              => '证书名称',
            'institution'       => '发证机构',
            'certificate_date'  => '发证日期',
            'exam_number'       => '准考证号',
            'certificate_number'=> '证书编号',
        ];
    }
    

    







    public function student()
    {
        return $this->belongsTo('Student', 'zz_student_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}

<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 修复班级表字段问题
 */
class FixBanjiFields extends Command
{
    protected function configure()
    {
        $this->setName('fix:banji-fields')
            ->setDescription('检查并修复班级表的location和enroll_year字段');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('检查班级表字段...');
        
        try {
            // 1. 检查表结构
            $output->writeln("\n📋 当前班级表结构：");
            $columns = Db::query("SHOW COLUMNS FROM zz_banji");
            $existingFields = [];
            foreach ($columns as $column) {
                $existingFields[] = $column['Field'];
                $output->writeln("  - {$column['Field']} ({$column['Type']})");
            }
            
            // 2. 检查缺失的字段
            $requiredFields = ['location', 'enroll_year'];
            $missingFields = [];
            
            foreach ($requiredFields as $field) {
                if (!in_array($field, $existingFields)) {
                    $missingFields[] = $field;
                }
            }
            
            if (empty($missingFields)) {
                $output->writeln("\n✅ 所有必需字段都存在");
                
                // 检查数据
                $this->checkData($output);
                
            } else {
                $output->writeln("\n❌ 缺失字段：" . implode(', ', $missingFields));
                
                // 询问是否添加字段
                $output->writeln("\n💡 建议操作：");
                $output->writeln("1. 添加缺失的字段到数据库");
                $output->writeln("2. 为现有数据设置默认值");
                
                if ($this->confirm($output, "是否自动添加缺失的字段？")) {
                    $this->addMissingFields($output, $missingFields);
                }
            }
            
        } catch (\Exception $e) {
            $output->writeln('<error>检查失败：' . $e->getMessage() . '</error>');
        }
        
        return 0;
    }
    
    /**
     * 添加缺失的字段
     */
    private function addMissingFields($output, $missingFields)
    {
        $output->writeln("\n🔧 添加缺失字段...");
        
        foreach ($missingFields as $field) {
            try {
                if ($field === 'location') {
                    Db::execute("ALTER TABLE zz_banji ADD COLUMN location VARCHAR(100) DEFAULT '' COMMENT '班级地点'");
                    $output->writeln("  ✅ 添加字段：location (班级地点)");
                }
                
                if ($field === 'enroll_year') {
                    Db::execute("ALTER TABLE zz_banji ADD COLUMN enroll_year INT(4) DEFAULT 0 COMMENT '入学年份'");
                    $output->writeln("  ✅ 添加字段：enroll_year (入学年份)");
                }
                
            } catch (\Exception $e) {
                $output->writeln("  ❌ 添加字段 {$field} 失败：" . $e->getMessage());
            }
        }
        
        // 为现有数据设置默认值
        $this->setDefaultValues($output);
    }
    
    /**
     * 为现有数据设置默认值
     */
    private function setDefaultValues($output)
    {
        $output->writeln("\n📝 设置默认值...");
        
        try {
            // 获取当前年份作为默认入学年份
            $currentYear = date('Y');
            $defaultYear = $currentYear - 1; // 假设是去年入学
            
            // 更新空的location字段
            $locationCount = Db::table('zz_banji')
                ->where('location', '')
                ->update(['location' => '待设置']);
            
            if ($locationCount > 0) {
                $output->writeln("  ✅ 更新了 {$locationCount} 条记录的班级地点为'待设置'");
            }
            
            // 更新空的enroll_year字段
            $yearCount = Db::table('zz_banji')
                ->where('enroll_year', 0)
                ->update(['enroll_year' => $defaultYear]);
            
            if ($yearCount > 0) {
                $output->writeln("  ✅ 更新了 {$yearCount} 条记录的入学年份为 {$defaultYear}");
            }
            
        } catch (\Exception $e) {
            $output->writeln("  ❌ 设置默认值失败：" . $e->getMessage());
        }
    }
    
    /**
     * 检查现有数据
     */
    private function checkData($output)
    {
        $output->writeln("\n📊 检查现有数据：");
        
        $total = Db::table('zz_banji')->count();
        $output->writeln("  - 总班级数：{$total}");
        
        if ($total > 0) {
            // 检查空的location
            $emptyLocation = Db::table('zz_banji')
                ->where('location', '')
                ->whereOr('location', 'null')
                ->count();
            
            if ($emptyLocation > 0) {
                $output->writeln("  - 班级地点为空的记录：{$emptyLocation}");
            }
            
            // 检查空的enroll_year
            $emptyYear = Db::table('zz_banji')
                ->where('enroll_year', 0)
                ->whereOr('enroll_year', 'null')
                ->count();
            
            if ($emptyYear > 0) {
                $output->writeln("  - 入学年份为空的记录：{$emptyYear}");
            }
            
            // 显示示例数据
            $samples = Db::table('zz_banji')->limit(3)->select();
            $output->writeln("  - 示例数据：");
            foreach ($samples as $sample) {
                $location = $sample['location'] ?? 'NULL';
                $year = $sample['enroll_year'] ?? 'NULL';
                $output->writeln("    班级：{$sample['name']}, 地点：{$location}, 年份：{$year}");
            }
        }
    }
    
    /**
     * 简单的确认提示
     */
    private function confirm($output, $question)
    {
        $output->writeln($question . " (y/n)");
        // 在实际使用中，这里应该读取用户输入
        // 为了自动化，这里返回true
        return true;
    }
}

define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'gaokaogd/index' + location.search,
                    add_url: 'gaokaogd/add',
                    edit_url: 'gaokaogd/edit',
                    del_url: 'gaokaogd/del',
                    multi_url: 'gaokaogd/multi',
                    import_url: 'gaokaogd/import',
                    table: 'gaokao_apply_local',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'registration_type', title: __('Registration_type'), searchList: {"3+证书":__('3+证书'),"三二分段":__('三二分段'),"五年一贯":__('五年一贯'),"其他":__('其他')}, formatter: Table.api.formatter.normal},
                        {field: 'household_type', title: __('Household_type'), searchList: {"居民":__('居民'),"农村":__('农村')}, formatter: Table.api.formatter.normal},
                        {field: 'household_address_city', title: __('Household_address_city'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'move_in_date', title: __('Move_in_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'school_roll_location', title: __('School_roll_location'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'full_three_years', title: __('Full_three_years'), searchList: {"是":__('是'),"否":__('否')}, formatter: Table.api.formatter.normal},
                        {field: 'school_roll_no', title: __('School_roll_no'), operate: 'LIKE'},
                        {field: 'id_card', title: __('Id_card'), operate: 'LIKE'},
                        {field: 'student.student_no', title: __('Student.student_no'), operate: 'LIKE'},
                        {field: 'student.name', title: __('Student.name'), operate: 'LIKE'},
                        {field: 'banji_name', title: '班级', operate: 'LIKE'},
                        {field: 'student.gender', title: __('Student.gender')},
                        {field: 'student.phone', title: __('Student.phone'), operate: 'LIKE'},
                        {field: 'student.father_phone', title: __('Student.father_phone'), operate: 'LIKE'},
                        {field: 'student.mother_phone', title: __('Student.mother_phone'), operate: 'LIKE'},
                        {field: 'student.native_place_city', title: __('Student.native_place_city'), operate: 'LIKE'},
                        {field: 'student.nation', title: __('Student.nation'), operate: 'LIKE'},
                        {field: 'student.political_status', title: __('Student.political_status'), operate: 'LIKE', formatter: Table.api.formatter.status},
                        {field: 'student.address', title: __('Student.address'), operate: 'LIKE'},
                        {field: 'student.status', title: __('Student.status'), formatter: Table.api.formatter.status},
                        {field: 'certificate.name', title: __('Certificate.name'), operate: 'LIKE'},
                        {field: 'certificate.certificate_date', title: __('Certificate.certificate_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'refresh-certificate',
                                    text: '刷新证书',
                                    title: '刷新证书信息',
                                    classname: 'btn btn-xs btn-info btn-refresh-certificate',
                                    icon: 'fa fa-refresh',
                                    click: function(e, value, row, index) {
                                        Controller.api.refreshSingleCertificate(row.id);
                                    }
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定全局同步证书按钮事件
            $('.btn-sync-certificates').on('click', function() {
                Controller.api.syncAllCertificates();
            });
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'gaokaogd/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '140px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'gaokaogd/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'gaokaogd/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            //alert("执行add方法");
            Controller.api.bindevent();

            // 添加我的自定义逻辑
            $(function() {

                // 定义一个函数用于判断是否显示迁入时间输入框
                function checkMoveInDate() {
                    var idCard = $('#c-id_card').val();
                    var moveInDateGroup = $('#move_in_date_group');
                    if (idCard.length === 18 && idCard.substr(0, 2) !== '44') {
                        moveInDateGroup.show();
                    } else {
                        moveInDateGroup.hide().find('input').val('');
                    }
                }

                // 身份证号码输入监听（虽然现在隐藏了，但保留以防万一手动输入情况）
                $('#c-id_card').on('input', checkMoveInDate);


                var idCardInput = $('#c-id_card');

                // 检查元素是否获取成功
                if (idCardInput.length === 0) {
                    alert('未找到 #c-id_card 元素');
                    return;
                }
              // 身份证号码输入监听
            idCardInput.on('input', function () {
            //console.log('input 事件触发');
            //console.log('this 的值:', this); // 检查 this 的指向

            // 保存 this 的引用
            var inputElement = this;
            var idCard = $(inputElement).val();
            //console.log('获取到的身份证号码:', idCard); // 在控制台输出获取到的值

            /*if (typeof idCard === 'undefined') {
                alert('获取到的身份证号码为 undefined');
            }*/
                
            var moveInDateGroup = $('#move_in_date_group');
            if (idCard && idCard.length === 18 && idCard.substr(0, 2) !== '44') {
                moveInDateGroup.show();
            } else {
                moveInDateGroup.hide().find('input').val('');
            }
        });

                // 表单提交验证
                $('#add-form').on('submit', function() {
                    var idCard = $('#c-id_card').val();
                    var moveInDate = $('#c-move_in_date').val();
                    if (idCard.length === 18 && idCard.substr(0, 2) !== '44' && !moveInDate) {
                        layer.msg('您是非 44 开头的广东户籍学生，请填写迁入时间', { icon: 2 });
                        return false;
                    }
                    return true;
                });

                // 定义获取学生信息的函数，便于复用
                function getStudentInfo(studentNo) {
                    if (studentNo) {
                        $.ajax({
                            url: getStudentInfoUrl,
                            method: 'POST',
                            data: { student_no: studentNo },
                            success: function(response) {
                                if (response.code === 1) {
                                    $('#student_name_display').text(response.data.name);
                                    $('#student_banji_display').text(response.data.banji_name || '未分配班级');
                                    $('#id_card_display').text(response.data.id_card);
                                    // 将获取到的身份证信息填充到隐藏的输入框中
                                    $('#c-id_card').val(response.data.id_card);
                                    // 获取到身份证信息后，触发一次判断是否显示迁入时间输入框的逻辑
                                    // 填充 student_id 到隐藏输入框
                                    $('#c-zz_student_id').val(response.data.student_id);
                                    // 填充学生证书 ID 到隐藏输入框
                                    var certificateId = response.data.certificate_id;
                                    $('#c-zz_student_certificate_id').val(certificateId || '');

                                    // 显示证书信息状态
                                    if (certificateId && certificateId !== '0' && certificateId !== 0) {
                                        $('#certificate_status').text('已有证书').css('color', 'green');
                                    } else {
                                        $('#certificate_status').text('暂无证书').css('color', 'orange');
                                    }

                                    // 获取到身份证信息后，触发一次判断是否显示迁入时间输入框的逻辑
                                    checkMoveInDate();
                                } else {
                                    $('#student_name_display').text('');
                                    $('#student_banji_display').text('');
                                    $('#id_card_display').text('');
                                    $('#c-id_card').val('');
                                    // 清空 student_id 输入框
                                    $('#c-zz_student_id').val('');
                                    // 清空学生证书 ID 输入框
                                    $('#c-zz_student_certificate_id').val('');
                                    $('#certificate_status').text('');
                                    // 清空学号时也检查一次
                                    checkMoveInDate();
                                    alert(response.msg);
                                }
                            },
                            error: function() {
                                alert('获取学生信息失败');
                            }
                        });
                    } else {
                        $('#student_name_display').text('');
                        $('#student_banji_display').text('');
                        $('#id_card_display').text('');
                        $('#c-id_card').val('');
                        // 清空 student_id 输入框
                        $('#c-zz_student_id').val('');
                        // 清空学生证书 ID 输入框
                        $('#c-zz_student_certificate_id').val('');
                        $('#certificate_status').text('');
                        // 清空学号时也检查一次
                        checkMoveInDate();
                    }
                }

                // 通过学号获取学生信息
                $('#c-student_no').on('input', function() {
                    var studentNo = $(this).val();
                    getStudentInfo(studentNo);
                });

                // 添加刷新证书信息按钮的点击事件
                $(document).on('click', '#refresh-certificate', function() {
                    var studentNo = $('#c-student_no').val();
                    if (studentNo) {
                        getStudentInfo(studentNo);
                        layer.msg('证书信息已刷新', {icon: 1});
                    } else {
                        layer.msg('请先输入学号', {icon: 2});
                    }
                });
            });
        },
          
        edit: function () {
            Controller.api.bindevent();

            // 编辑页面的刷新证书按钮
            $('#refresh-certificate-edit').on('click', function() {
                var recordId = $('input[name="row[id]"]').val();
                if (recordId) {
                    Controller.api.refreshSingleCertificateEdit(recordId);
                } else {
                    layer.msg('无法获取记录ID', {icon: 2});
                }
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },

            // 全局同步证书信息
            syncAllCertificates: function() {
                layer.confirm('确定要同步所有学生的证书信息吗？', {
                    title: '确认同步',
                    icon: 3
                }, function(index) {
                    layer.close(index);
                    var loading = layer.load(1, {shade: [0.3, '#000']});

                    $.ajax({
                        url: 'gaokaogd/synccertificates',
                        method: 'POST',
                        data: {},
                        success: function(response) {
                            layer.close(loading);
                            if (response.code === 1) {
                                layer.msg('证书信息同步成功', {icon: 1});
                                $('#table').bootstrapTable('refresh');
                            } else {
                                layer.msg(response.msg || '同步失败', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.close(loading);
                            layer.msg('同步失败，请重试', {icon: 2});
                        }
                    });
                });
            },

            // 单个记录刷新证书信息
            refreshSingleCertificate: function(id) {
                var loading = layer.load(1, {shade: [0.3, '#000']});

                $.ajax({
                    url: 'gaokaogd/refreshcertificate',
                    method: 'POST',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        layer.close(loading);
                        if (response.code === 1) {
                            layer.msg('证书信息已刷新', {icon: 1});
                            $('#table').bootstrapTable('refresh');
                        } else {
                            layer.msg(response.msg || '刷新失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loading);
                        layer.msg('刷新失败，请重试', {icon: 2});
                    }
                });
            },

            // 编辑页面刷新证书信息
            refreshSingleCertificateEdit: function(id) {
                var loading = layer.load(1, {shade: [0.3, '#000']});

                $.ajax({
                    url: 'gaokaogd/refreshcertificate',
                    method: 'POST',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        layer.close(loading);
                        if (response.code === 1) {
                            layer.msg('证书信息已刷新', {icon: 1});
                            // 刷新页面以显示最新的证书信息
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            layer.msg(response.msg || '刷新失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loading);
                        layer.msg('刷新失败，请重试', {icon: 2});
                    }
                });
            }
        }

        



    };
    return Controller;
});

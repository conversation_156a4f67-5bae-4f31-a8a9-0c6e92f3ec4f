define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'stucertifi/index' + location.search,
                    add_url: 'stucertifi/add',
                    edit_url: 'stucertifi/edit',
                    del_url: 'stucertifi/del',
                    multi_url: 'stucertifi/multi',
                    import_url: 'stucertifi/import',
                    table: 'student_certificate',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'institution', title: __('Institution'), operate: 'LIKE'},
                        {field: 'certificate_date', title: __('Certificate_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'exam_number', title: __('Exam_number'), operate: 'LIKE'},
                        {field: 'certificate_number', title: __('Certificate_number'), operate: 'LIKE'},
                        {field: 'student.student_no', title: __('Student.student_no'), operate: 'LIKE'},
                        {field: 'student.name', title: __('Student.name'), operate: 'LIKE'},
                        {field: 'banji_name', title: '班级', operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();

            // 添加学号搜索功能
            $('#c-student_no').on('input', function() {
                var studentNo = $(this).val();

                if (studentNo) {
                    $.ajax({
                        url: 'student/getstudentinfo',
                        method: 'POST',
                        data: { student_no: studentNo },
                        success: function(response) {
                            if (response.code === 1) {
                                var studentInfo = response.data;
                                $('#student_info_display').html(
                                    '<strong>姓名：</strong>' + studentInfo.name +
                                    ' <strong>身份证：</strong>' + studentInfo.id_card +
                                    ' <strong>班级：</strong>' + (studentInfo.banji_name || '未分配')
                                ).css('color', '#333');

                                // 设置隐藏的学生ID
                                $('#c-zz_student_id').val(studentInfo.student_id);
                            } else {
                                $('#student_info_display').html(
                                    '<span style="color: #d9534f;">未找到该学号对应的学生</span>'
                                );
                                $('#c-zz_student_id').val('');
                            }
                        },
                        error: function() {
                            $('#student_info_display').html(
                                '<span style="color: #d9534f;">查询学生信息失败</span>'
                            );
                            $('#c-zz_student_id').val('');
                        }
                    });
                } else {
                    $('#student_info_display').html(
                        '<span style="color: #999;">请输入学号获取学生信息</span>'
                    );
                    $('#c-zz_student_id').val('');
                }
            });
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

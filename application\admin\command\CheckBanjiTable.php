<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 检查班级表结构和数据
 */
class CheckBanjiTable extends Command
{
    protected function configure()
    {
        $this->setName('check:banji-table')
            ->setDescription('检查班级表的结构和数据');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('检查班级表结构和数据...');
        
        try {
            // 1. 检查表结构
            $output->writeln("\n📋 班级表结构：");
            $columns = Db::query("SHOW COLUMNS FROM zz_banji");
            foreach ($columns as $column) {
                $output->writeln("  - {$column['Field']} ({$column['Type']}) " . 
                    ($column['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . 
                    ($column['Default'] !== null ? " DEFAULT '{$column['Default']}'" : ''));
            }
            
            // 2. 检查数据样本
            $output->writeln("\n📊 班级数据样本：");
            $samples = Db::table('zz_banji')->limit(3)->select();
            if (empty($samples)) {
                $output->writeln("  - 暂无班级数据");
            } else {
                foreach ($samples as $sample) {
                    $output->writeln("  - ID:{$sample['id']}, 名称:{$sample['name']}");
                    foreach ($sample as $key => $value) {
                        if (!in_array($key, ['id', 'name'])) {
                            $output->writeln("    {$key}: " . ($value ?? 'NULL'));
                        }
                    }
                    $output->writeln("");
                }
            }
            
            // 3. 统计信息
            $total = Db::table('zz_banji')->count();
            $output->writeln("📈 总班级数：{$total}");
            
        } catch (\Exception $e) {
            $output->writeln('<error>检查失败：' . $e->getMessage() . '</error>');
        }
        
        return 0;
    }
}

<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 诊断导入功能问题
 */
class DiagnoseImportIssues extends Command
{
    protected function configure()
    {
        $this->setName('diagnose:import-issues')
            ->setDescription('诊断学生和证书导入功能的问题');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('🔍 诊断导入功能问题...');
        
        // 1. 检查学生数据
        $this->checkStudentData($output);
        
        // 2. 检查证书数据
        $this->checkCertificateData($output);
        
        // 3. 检查导入配置
        $this->checkImportConfig($output);
        
        // 4. 提供修复建议
        $this->provideSolutions($output);
        
        return 0;
    }
    
    /**
     * 检查学生数据
     */
    private function checkStudentData($output)
    {
        $output->writeln("\n📚 检查学生数据：");
        
        try {
            $studentCount = Db::table('zz_student')->count();
            $output->writeln("  - 学生总数：{$studentCount}");
            
            if ($studentCount > 0) {
                // 检查学号格式
                $samples = Db::table('zz_student')
                    ->field('id, student_no, name')
                    ->limit(5)
                    ->select();
                
                $output->writeln("  - 学号样本：");
                foreach ($samples as $student) {
                    $output->writeln("    ID:{$student['id']}, 学号:{$student['student_no']}, 姓名:{$student['name']}");
                }
                
                // 检查学号唯一性
                $duplicateStudentNos = Db::query("
                    SELECT student_no, COUNT(*) as count 
                    FROM zz_student 
                    GROUP BY student_no 
                    HAVING COUNT(*) > 1
                ");
                
                if (!empty($duplicateStudentNos)) {
                    $output->writeln("  ⚠️  发现重复学号：");
                    foreach ($duplicateStudentNos as $dup) {
                        $output->writeln("    学号 {$dup['student_no']} 重复 {$dup['count']} 次");
                    }
                }
            } else {
                $output->writeln("  ⚠️  学生表为空，这可能是证书导入失败的原因");
            }
            
        } catch (\Exception $e) {
            $output->writeln("  ❌ 检查学生数据失败：" . $e->getMessage());
        }
    }
    
    /**
     * 检查证书数据
     */
    private function checkCertificateData($output)
    {
        $output->writeln("\n📜 检查证书数据：");
        
        try {
            $certificateCount = Db::table('zz_student_certificate')->count();
            $output->writeln("  - 证书总数：{$certificateCount}");
            
            if ($certificateCount > 0) {
                // 检查证书样本
                $samples = Db::query("
                    SELECT c.id, c.name, c.zz_student_id, s.student_no, s.name as student_name
                    FROM zz_student_certificate c
                    LEFT JOIN zz_student s ON c.zz_student_id = s.id
                    LIMIT 5
                ");
                
                $output->writeln("  - 证书样本：");
                foreach ($samples as $cert) {
                    $studentInfo = $cert['student_no'] ? "学号:{$cert['student_no']}, 姓名:{$cert['student_name']}" : "学生信息缺失";
                    $output->writeln("    证书:{$cert['name']}, {$studentInfo}");
                }
                
                // 检查孤立证书（没有关联学生的）
                $orphanCertificates = Db::query("
                    SELECT COUNT(*) as count
                    FROM zz_student_certificate c
                    LEFT JOIN zz_student s ON c.zz_student_id = s.id
                    WHERE s.id IS NULL
                ");
                
                if ($orphanCertificates[0]['count'] > 0) {
                    $output->writeln("  ⚠️  发现 {$orphanCertificates[0]['count']} 个孤立证书（没有关联学生）");
                }
            }
            
        } catch (\Exception $e) {
            $output->writeln("  ❌ 检查证书数据失败：" . $e->getMessage());
        }
    }
    
    /**
     * 检查导入配置
     */
    private function checkImportConfig($output)
    {
        $output->writeln("\n⚙️  检查导入配置：");
        
        // 检查学生导入字段映射
        $studentFields = [
            'student_no' => '学号',
            'name' => '姓名',
            'id_card' => '身份证号',
            'gender' => '性别(单选)',
        ];
        
        $output->writeln("  - 学生导入字段映射：");
        foreach ($studentFields as $field => $header) {
            $output->writeln("    {$field} => '{$header}'");
        }
        
        // 检查证书导入字段
        $output->writeln("  - 证书导入期望字段：");
        $output->writeln("    学号 => 用于查找学生");
        $output->writeln("    name/证书名称 => 证书名称");
        $output->writeln("    institution => 颁发机构");
        $output->writeln("    certificate_date => 获证日期");
    }
    
    /**
     * 提供解决方案
     */
    private function provideSolutions($output)
    {
        $output->writeln("\n💡 解决方案建议：");
        
        $output->writeln("1. 学生导入问题：");
        $output->writeln("   - 确保Excel第一行表头为：学号、身份证号、姓名、性别(单选)等");
        $output->writeln("   - 检查学号格式是否为纯数字");
        $output->writeln("   - 确保没有重复的学号");
        
        $output->writeln("\n2. 证书导入问题：");
        $output->writeln("   - 确保学生数据已经导入");
        $output->writeln("   - Excel中的学号必须在学生表中存在");
        $output->writeln("   - 证书名称不能为空");
        
        $output->writeln("\n3. 通用建议：");
        $output->writeln("   - 使用系统提供的模板");
        $output->writeln("   - 先导入学生，再导入证书");
        $output->writeln("   - 检查日志文件获取详细错误信息");
        
        $output->writeln("\n4. 如果问题持续：");
        $output->writeln("   - 可以考虑简化导入验证逻辑");
        $output->writeln("   - 或者回滚到更简单的版本");
    }
}

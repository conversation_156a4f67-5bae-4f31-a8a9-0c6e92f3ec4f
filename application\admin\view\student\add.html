<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Student_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-student_no" data-rule="required" class="form-control" name="row[student_no]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_banji_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zz_banji_id" data-rule="required" min="0" data-source="banji/index" class="form-control selectpage" name="row[zz_banji_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zz_dorm_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zz_dorm_id" min="0" data-rule="required" data-source="dorm/index" data-field="name" class="form-control selectpage" name="row[zz_dorm_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Id_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-id_card" data-rule="required" class="form-control" name="row[id_card]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-gender" data-rule="required" class="form-control selectpicker" name="row[gender]">
                {foreach name="genderList" item="vo"}
                    <option value="{$key}" {in name="key" value="male"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone" data-rule="required" class="form-control" name="row[phone]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Father_phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-father_phone" class="form-control" name="row[father_phone]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mother_phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mother_phone" class="form-control" name="row[mother_phone]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Native_place_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-native_place_city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[native_place_city]" type="text"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('household_register_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-household_register_city" class="form-control" data-toggle="city-picker" name="row[household_register_city]" type="text"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nation')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nation" data-rule="required" class="form-control" name="row[nation]" type="text" value="汉族">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Political_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-political_status" data-rule="required" class="form-control" name="row[political_status]" type="text" value="群众">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" data-rule="required" class="form-control" name="row[address]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="studying"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 学生证书信息管理
 *
 * @icon fa fa-circle-o
 */
class Stucertifi extends Backend
{

    /**
     * Stucertifi模型对象
     * @var \app\admin\model\Stucertifi
     */
    protected $model = null;

    /**
     * 导入字段映射
     */
    protected $importFields = [
        'name'              => '证书名称',
        'institution'       => '颁发机构',
        'certificate_date'  => '获证日期',
        'exam_number'       => '准考证号',
        'certificate_number'=> '证书编号',
        'student_no'        => '学号',
        'zz_student_id'     => '学生ID'
    ];

    /**
     * 导入头类型：使用字段名而不是注释
     */
    protected $importHeadType = 'name';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Stucertifi;

        // 设置关联查询
        $this->relationSearch = true;
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['student'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            // 批量获取班级信息，避免N+1查询问题
            $studentIds = [];
            foreach ($list as $row) {
                if ($row->getRelation('student') && $row->getRelation('student')->zz_banji_id) {
                    $studentIds[] = $row->getRelation('student')->zz_banji_id;
                }
            }

            // 一次性查询所有需要的班级信息
            $banjiList = [];
            if (!empty($studentIds)) {
                $banjiData = \app\admin\model\Banji::whereIn('id', array_unique($studentIds))->select();
                foreach ($banjiData as $banji) {
                    $banjiList[$banji->id] = $banji->name;
                }
            }

            foreach ($list as $row) {
                $row->visible(['student']);
                $row->getRelation('student')->visible(['student_no','name']);

                // 设置班级名称
                if ($row->getRelation('student') && $row->getRelation('student')->zz_banji_id) {
                    $row->banji_name = $banjiList[$row->getRelation('student')->zz_banji_id] ?? '';
                } else {
                    $row->banji_name = '';
                }

                // 设置可见字段，包含班级名称
                $row->visible(['id','name','institution','certificate_date','exam_number','certificate_number','createtime','banji_name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }







    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    public function import(){ //导入
        // 如果是GET请求，显示导入页面
        if ($this->request->isGet()) {
            return parent::import();
        }

        // 使用自定义导入逻辑，绕过FastAdmin的字段映射问题
        return $this->customImport();
    }

    /**
     * 自定义导入方法
     */
    private function customImport()
    {
        $file = $this->request->request('file');
        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }

        $filePath = ROOT_PATH . DS . 'public' . DS . ltrim($file, '/\\');
        if (!is_file($filePath)) {
            $this->error(__('No results were found'));
        }

        // 读取Excel文件
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            $this->error(__('Unknown data format'));
        }

        if ($ext === 'xls') {
            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xls();
        } else {
            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
        }

        try {
            $spreadsheet = $reader->load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();

            // 读取表头
            $headers = [];
            for ($col = 'A'; $col <= $highestColumn; $col++) {
                $headers[] = $worksheet->getCell($col . '1')->getValue();
            }

            // Excel表头读取完成

            // 自定义字段映射
            $fieldMapping = [
                '学号' => 'student_no',
                '证书名称' => 'name',
                'name' => 'name',
                '颁发机构' => 'institution',
                '发证机构' => 'institution',
                'institution' => 'institution',
                '获证日期' => 'certificate_date',
                'certificate_date' => 'certificate_date'
            ];

            $insert = [];
            $successCount = 0;

            // 处理数据行 - 收集错误而不是立即终止
            $errors = [];
            for ($row = 2; $row <= $highestRow; $row++) {
                $data = [];
                $col = 'A';
                foreach ($headers as $header) {
                    $value = $worksheet->getCell($col . $row)->getValue();
                    if (isset($fieldMapping[$header])) {
                        $data[$fieldMapping[$header]] = $value;
                    }
                    $data[$header] = $value; // 保留原始表头
                    $col++;
                }

                try {
                    $processedData = $this->processRow($data, $row);
                    if ($processedData) {
                        $insert[] = $processedData;
                        $successCount++;
                    }
                } catch (\Exception $e) {
                    // 收集错误信息，不立即终止
                    $errors[] = "第{$row}行：" . $e->getMessage();
                }
            }

            // 根据处理结果返回相应信息
            if (empty($insert) && !empty($errors)) {
                // 没有成功数据，只有错误
                $errorMsg = "导入失败，所有数据都有问题：\n" . implode("\n", array_slice($errors, 0, 5));
                if (count($errors) > 5) {
                    $errorMsg .= "\n...还有" . (count($errors) - 5) . "个错误";
                }
                $this->error($errorMsg);
            } elseif (empty($insert)) {
                // 没有任何数据
                $this->error('没有有效的数据行');
            }

            // 有数据需要插入
            try {
                $this->model->saveAll($insert);
            } catch (\Exception $insertError) {
                // 数据插入失败，返回详细错误信息
                $this->error('数据插入失败：' . $insertError->getMessage());
            }

            // 【关键修复】根据实际情况返回准确的结果信息
            if (empty($errors)) {
                // 全部成功
                $this->success("导入成功！共导入 {$successCount} 条证书记录");
            } else {
                // 部分成功，部分失败
                $message = "导入完成！成功 {$successCount} 条，失败 " . count($errors) . " 条\n\n失败原因：\n";
                $message .= implode("\n", array_slice($errors, 0, 3));
                if (count($errors) > 3) {
                    $message .= "\n...还有" . (count($errors) - 3) . "个错误";
                }

                // 虽然有错误，但有成功数据，仍然返回success（因为数据已插入）
                // 但提供详细的错误信息
                $this->success($message);
            }

        } catch (\think\exception\HttpResponseException $e) {
            // 【关键修复】重新抛出HTTP响应异常
            // FastAdmin的success()和error()方法通过抛出HttpResponseException来工作
            // 这个异常必须被允许正常传播，不能被catch捕获
            // 否则会导致"数据插入成功但页面显示失败"的问题
            throw $e;
        } catch (\Exception $e) {
            $this->error('导入失败：' . $e->getMessage());
        }
    }

    /**
     * 处理单行数据
     */
    private function processRow($data, $rowNumber)
    {

        // 获取学号
        $studentNo = trim($data['student_no'] ?? '');
        if (empty($studentNo)) {
            throw new \Exception("学号不能为空");
        }

        // 查找学生
        $student = \app\admin\model\Student::where('student_no', $studentNo)->find();
        if (!$student) {
            throw new \Exception("找不到学号为【{$studentNo}】的学生");
        }

        // 获取证书名称
        $certificateName = trim($data['name'] ?? '');
        if (empty($certificateName)) {
            throw new \Exception("学号【{$studentNo}】的证书名称不能为空");
        }

        // 处理日期格式
        $certificateDate = $this->convertExcelDate($data['certificate_date'] ?? null);

        // 构建最终数据
        $result = [
            'zz_student_id' => $student->id,
            'name' => $certificateName,
            'institution' => trim($data['institution'] ?? ''),
            'certificate_date' => $certificateDate,
            'exam_number' => trim($data['exam_number'] ?? '') ?: null,
            'certificate_number' => trim($data['certificate_number'] ?? '') ?: null,
            'createtime' => time()
        ];

        return $result;
    }

    /**
     * 转换Excel日期格式
     */
    private function convertExcelDate($value)
    {
        if (empty($value)) {
            return null;
        }

        // 如果已经是标准日期格式，直接返回
        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
            return $value;
        }

        // 如果是Excel数字日期格式
        if (is_numeric($value)) {
            try {
                // Excel日期从1900年1月1日开始计算
                // 但Excel错误地认为1900年是闰年，所以需要减去1天
                $unixTimestamp = ($value - 25569) * 86400;

                // 验证时间戳是否合理（1970-2100年之间）
                if ($unixTimestamp > 0 && $unixTimestamp < 4102444800) {
                    $date = date('Y-m-d', $unixTimestamp);
                    \think\Log::write("Excel日期转换：{$value} -> {$date}", 'info');
                    return $date;
                }
            } catch (\Exception $e) {
                \think\Log::write("Excel日期转换失败：{$value}, 错误：" . $e->getMessage(), 'error');
            }
        }

        // 尝试其他日期格式
        $dateFormats = [
            'Y-m-d',
            'Y/m/d',
            'Y.m.d',
            'd/m/Y',
            'm/d/Y',
            'd-m-Y',
            'm-d-Y'
        ];

        foreach ($dateFormats as $format) {
            try {
                $dateObj = \DateTime::createFromFormat($format, $value);
                if ($dateObj !== false) {
                    $date = $dateObj->format('Y-m-d');
                    \think\Log::write("日期格式转换：{$value} ({$format}) -> {$date}", 'info');
                    return $date;
                }
            } catch (\Exception $e) {
                // 继续尝试下一个格式
            }
        }

        // 最后尝试strtotime
        try {
            $timestamp = strtotime($value);
            if ($timestamp !== false) {
                $date = date('Y-m-d', $timestamp);
                \think\Log::write("字符串日期转换：{$value} -> {$date}", 'info');
                return $date;
            }
        } catch (\Exception $e) {
            \think\Log::write("字符串日期转换失败：{$value}, 错误：" . $e->getMessage(), 'error');
        }

        // 如果都转换失败，记录警告并返回null
        \think\Log::write("日期格式无法识别，已设为空：{$value}", 'warning');
        return null;
    }



}

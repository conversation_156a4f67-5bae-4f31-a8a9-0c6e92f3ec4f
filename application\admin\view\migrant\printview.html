<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>随迁子女信息打印</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        
        .header .subtitle {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        
        .print-info {
            text-align: right;
            margin-bottom: 20px;
            font-size: 11px;
            color: #999;
        }
        
        .record-item {
            border: 1px solid #ddd;
            margin-bottom: 20px;
            padding: 15px;
            page-break-inside: avoid;
            background: #fafafa;
        }
        
        .record-header {
            background: #4CAF50;
            color: white;
            padding: 8px 15px;
            margin: -15px -15px 15px -15px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .record-content {
            display: table;
            width: 100%;
        }
        
        .field-row {
            display: table-row;
        }
        
        .field-label {
            display: table-cell;
            width: 120px;
            padding: 5px 10px 5px 0;
            font-weight: bold;
            vertical-align: top;
            color: #555;
        }
        
        .field-value {
            display: table-cell;
            padding: 5px 0;
            vertical-align: top;
        }
        
        .status-pass {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .status-reject {
            color: #f44336;
            font-weight: bold;
        }
        
        .status-pending {
            color: #ff9800;
            font-weight: bold;
        }
        
        .certificate-yes {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .certificate-no {
            color: #999;
        }
        
        @media print {
            body {
                margin: 0;
                font-size: 11px;
            }
            
            .header {
                margin-bottom: 20px;
            }
            
            .record-item {
                margin-bottom: 15px;
                padding: 10px;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        <i class="fa fa-print"></i> 打印
    </button>
    
    <div class="header">
        <h1>随迁子女信息表</h1>
        <div class="subtitle">共 {$list|count} 条记录</div>
    </div>
    
    <div class="print-info no-print">
        打印时间：{:date('Y-m-d H:i:s')} | 操作员：{:session('admin.username')}
    </div>
    
    {volist name="list" id="item" key="index"}
    <div class="record-item">
        <div class="record-header">
            第 {$index} 条 - {$item['student_name']|default='未知姓名'} ({$item['student_number']|default='无学号'})
        </div>
        
        <div class="record-content">
            <div class="field-row">
                <div class="field-label">学号：</div>
                <div class="field-value">{$item['student_number']|default='--'}</div>
            </div>

            <div class="field-row">
                <div class="field-label">姓名：</div>
                <div class="field-value">{$item['student_name']|default='--'}</div>
            </div>

            <div class="field-row">
                <div class="field-label">身份证号：</div>
                <div class="field-value">{$item['student_id_card']|default='--'}</div>
            </div>

            <div class="field-row">
                <div class="field-label">报考类别：</div>
                <div class="field-value">{$item['application_category']|default='--'}</div>
            </div>

            <div class="field-row">
                <div class="field-label">证书状态：</div>
                <div class="field-value">
                    {if condition="$item['has_certificate'] == '是'"}
                        <span class="certificate-yes">有证书</span>
                    {else/}
                        <span class="certificate-no">无证书</span>
                    {/if}
                </div>
            </div>

            <div class="field-row">
                <div class="field-label">审核状态：</div>
                <div class="field-value">
                    {if condition="$item['school_review_status_type'] == '通过'"}
                        <span class="status-pass">通过</span>
                    {elseif condition="$item['school_review_status_type'] == '未通过'"}
                        <span class="status-reject">未通过</span>
                    {else/}
                        <span class="status-pending">未审查</span>
                    {/if}
                </div>
            </div>

            {if condition="$item['review_opinion']"}
            <div class="field-row">
                <div class="field-label">审核意见：</div>
                <div class="field-value">{$item['review_opinion']}</div>
            </div>
            {/if}

            {if condition="$item['remark']"}
            <div class="field-row">
                <div class="field-label">备注：</div>
                <div class="field-value">{$item['remark']}</div>
            </div>
            {/if}

            <div class="field-row">
                <div class="field-label">创建时间：</div>
                <div class="field-value">{$item['createtime']|date='Y-m-d H:i:s',###}</div>
            </div>
        </div>
    </div>
    {/volist}
    
    <script>
        // 自动打印（可选）
        // window.onload = function() {
        //     window.print();
        // };
    </script>
</body>
</html>

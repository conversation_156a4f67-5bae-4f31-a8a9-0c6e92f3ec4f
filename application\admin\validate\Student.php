<?php

namespace app\admin\validate;

use think\Validate;

class Student extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'student_no' => 'require|unique:student',
        'id_card'    => 'require|unique:student',
        'name'       => 'require',
        'gender'     => 'require|in:male,female',
        'status'     => 'in:studying,graduated'
    ];
    /**
     * 提示消息
     */
    protected $message = [
    ];
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['student_no', 'id_card', 'name', 'gender', 'status'],
        'edit' => ['student_no', 'id_card', 'name', 'gender', 'status'],
    ];

    /**
     * 编辑场景验证规则
     */
    public function sceneEdit()
    {
        return $this->only(['student_no', 'id_card', 'name', 'gender', 'status'])
                    ->remove('student_no', 'unique')
                    ->remove('id_card', 'unique');
    }

    /**
     * 自定义身份证验证器
     */
    protected function idCard($value)
    {
        return preg_match('/^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|1\d|2\d|30|31)\d{3}(?:\d|X|x)$/', $value) ||
               preg_match('/^[1-9]\d{7}(?:0\d|10|11|12)(?:0[1-9]|1\d|2\d|30|31)\d{3}$/', $value);
    }

}
